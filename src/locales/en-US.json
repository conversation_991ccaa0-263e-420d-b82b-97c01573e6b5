{"navigation": {"dashboard": "Dashboard", "tasks": "Tasks", "apps": "Apps", "chats": "Chats", "users": "Users", "settings": "Settings", "helpCenter": "Help Center", "general": "General", "pages": "Pages", "other": "Other", "auth": "<PERSON><PERSON>", "errors": "Errors", "profile": "Profile", "account": "Account", "appearance": "Appearance", "notifications": "Notifications", "display": "Display", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "forgotPassword": "Forgot Password", "otp": "OTP", "userManagement": "User Management", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not Found", "internalServerError": "Internal Server Error", "maintenanceError": "Maintenance Error", "securedByClerk": "Secured by Clerk"}, "dashboard": {"title": "Dashboard", "download": "Download", "overview": "Overview", "analytics": "Analytics", "reports": "Reports", "notifications": "Notifications", "customers": "Customers", "products": "Products", "totalRevenue": "Total Revenue", "subscriptions": "Subscriptions", "sales": "Sales", "activeNow": "Active Now", "fromLastMonth": "from last month", "sinceLastHour": "since last hour", "recentSales": "Recent Sales", "salesThisMonth": "You made 265 sales this month."}, "tasks": {"title": "Tasks", "description": "Here's a list of your tasks for this month!", "new": "New Task", "edit": "Edit Task", "delete": "Delete Task", "status": "Status", "priority": "Priority", "label": "Label", "todo": "Todo", "inProgress": "In Progress", "done": "Done", "canceled": "Canceled", "backlog": "Backlog", "low": "Low", "medium": "Medium", "high": "High", "bug": "Bug", "feature": "Feature", "documentation": "Documentation"}, "users": {"title": "Users", "description": "Manage your team members and their account permissions here.", "new": "New User", "edit": "Edit User", "delete": "Delete User", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "email": "Email", "phoneNumber": "Phone Number", "role": "Role", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "active": "Active", "inactive": "Inactive", "invited": "Invited", "suspended": "Suspended", "superadmin": "Super Admin", "admin": "Admin", "cashier": "Cashier", "manager": "Manager"}, "apps": {"title": "App Integrations", "description": "Here's a list of your apps for the integration!", "filterApps": "Filter apps...", "allApps": "All Apps", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect", "disconnect": "Disconnect", "configure": "Configure"}, "chats": {"title": "Chats", "newChat": "New Chat", "searchChats": "Search chats...", "typeMessage": "Type a message...", "send": "Send", "online": "Online", "offline": "Offline"}, "settings": {"title": "Settings", "description": "Manage your account settings and preferences."}, "buttons": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "submit": "Submit", "reset": "Reset", "back": "Back", "next": "Next", "previous": "Previous", "confirm": "Confirm", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload"}, "common": {"loading": "Loading...", "noResults": "No results.", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "ok": "OK", "total": "Total", "selected": "Selected", "all": "All", "none": "None", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "welcome": "Welcome"}, "forms": {"required": "This field is required", "invalidEmail": "Invalid email address", "passwordTooShort": "Password must be at least 7 characters long", "passwordsNotMatch": "Passwords do not match", "pleaseEnterEmail": "Please enter your email", "pleaseEnterPassword": "Please enter your password", "title": "Title", "description": "Description", "name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password"}, "messages": {"sessionExpired": "Session expired!", "internalServerError": "Internal Server Error!", "contentNotModified": "Content not modified!", "operationSuccessful": "Operation successful!", "operationFailed": "Operation failed!", "dataSubmitted": "You submitted the following values:"}, "language": {"chinese": "中文", "english": "English", "switchLanguage": "Switch Language"}, "marketing": {"features": "Features", "pricing": "Pricing", "blog": "Blog", "about": "About", "contact": "Contact", "dashboard": "Dashboard", "getStarted": "Get Started", "documentation": "Documentation", "changelog": "Changelog", "careers": "Careers", "helpCenter": "Help Center", "community": "Community", "support": "Support", "status": "Status", "privacy": "Privacy Policy", "terms": "Terms of Service", "security": "Security", "cookies": "<PERSON><PERSON>", "product": "Product", "company": "Company", "resources": "Resources", "legal": "Legal", "footerDescription": "Build beautiful admin dashboards with modern React components.", "allRightsReserved": "All rights reserved.", "builtWith": "Built with", "usingReact": "and React", "heroTitle": "Build Modern Admin Dashboards", "heroSubtitle": "Create beautiful, responsive admin interfaces quickly with Shadcn UI components. Fully open source and customizable.", "viewDemo": "View Demo", "learnMore": "Learn More"}, "blog": {"title": "Blog", "subtitle": "Latest articles and product updates", "readMore": "Read More", "readTime": "min read", "publishedOn": "Published on", "author": "Author", "category": "Category", "tags": "Tags", "relatedPosts": "Related Posts", "searchPlaceholder": "Search articles...", "noResults": "No articles found", "allCategories": "All Categories", "latestPosts": "Latest Posts", "popularPosts": "Popular Posts", "backToBlog": "Back to Blog", "shareArticle": "Share Article"}, "api": {"errors": {"badRequest": "Bad Request", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not Found", "methodNotAllowed": "Method Not Allowed", "conflict": "Conflict", "validationError": "Validation Error", "tooManyRequests": "Too Many Requests", "internalServerError": "Internal Server Error", "badGateway": "Bad Gateway", "serviceUnavailable": "Service Unavailable", "gatewayTimeout": "Gateway Timeout", "businessError": "Business Logic Error", "authenticationError": "Authentication Failed", "authorizationError": "Authorization Failed", "resourceNotFound": "Resource Not Found", "resourceConflict": "Resource Conflict", "rateLimitExceeded": "Rate Limit Exceeded", "networkError": "Network Error", "timeout": "Request Timeout", "cancelled": "Request Cancelled", "connectionError": "Connection Error", "unknownError": "Unknown Error", "serverError": "Server Error", "clientError": "<PERSON><PERSON>", "networkErrorDesc": "Please check your network connection", "unauthorizedDesc": "Please login again", "forbiddenDesc": "You do not have permission to perform this action", "serverErrorDesc": "Server is temporarily unavailable"}}, "about": {"title": "About Us", "subtitle": "Learn about our story, mission, and team", "ourStory": "Our Story", "ourMission": "Our Mission", "ourVision": "Our Vision", "ourTeam": "Our Team", "ourValues": "Our Values", "joinUs": "Join Us", "storyContent": "Shadcn Admin started with a simple idea: to help developers build beautiful, modern admin interfaces quickly. We believe that great tools unleash creativity and let developers focus on what truly matters.", "missionContent": "Our mission is to provide developers with the best React admin interface solution, driving community growth through open source.", "visionContent": "We aspire to become the go-to admin interface framework for developers worldwide, enabling everyone to build professional-grade applications with ease.", "innovation": "Innovation", "innovationDesc": "Continuously exploring new technologies and pushing the boundaries of interface design", "openSource": "Open Source", "openSourceDesc": "Believing in the power of open source and growing together with the community", "quality": "Quality", "qualityDesc": "Pursuing excellence with attention to every detail", "community": "Community", "communityDesc": "Building an inclusive and friendly developer community"}, "contact": {"title": "Contact Us", "subtitle": "Have questions or suggestions? We'd love to hear from you", "getInTouch": "Get in Touch", "sendMessage": "Send Message", "yourName": "Your Name", "yourEmail": "Your Email", "subject": "Subject", "message": "Message", "sendButton": "Send Message", "messageSent": "Message sent successfully!", "messageError": "Failed to send message. Please try again.", "email": "Email", "phone": "Phone", "address": "Address", "businessHours": "Business Hours", "mondayToFriday": "Monday to Friday", "timeRange": "9:00 AM - 6:00 PM", "responseTime": "We typically respond within 24 hours", "followUs": "Follow Us", "socialMedia": "Social Media"}, "privacy": {"title": "Privacy Policy", "lastUpdated": "Last Updated", "introduction": "Introduction", "dataCollection": "Data Collection", "dataUsage": "Data Usage", "dataSharing": "Data Sharing", "dataSecurity": "Data Security", "userRights": "User Rights", "cookiePolicy": "<PERSON><PERSON>", "contactUs": "Contact Us", "introContent": "This privacy policy explains how we collect, use, and protect your personal information. We are committed to protecting your privacy and complying with all applicable data protection laws.", "collectionContent": "We may collect the following types of information: personal identification information (such as name, email address), usage data (such as page visit records), and technical information (such as IP address, browser type).", "usageContent": "We use the collected information to: provide and improve our services, communicate with you, send important notifications, and analyze usage patterns to enhance user experience.", "sharingContent": "We do not sell, trade, or transfer your personal information to third parties without your consent or legal requirement.", "securityContent": "We employ industry-standard security measures to protect your personal information, including encrypted transmission, secure storage, and access controls.", "rightsContent": "You have the right to access, correct, delete, or restrict the processing of your personal data. To exercise these rights, please contact us."}, "terms": {"title": "Terms of Service", "lastUpdated": "Last Updated", "acceptance": "Acceptance of Terms", "serviceDescription": "Service Description", "userResponsibilities": "User Responsibilities", "intellectualProperty": "Intellectual Property", "limitation": "Limitation of Liability", "termination": "Termination", "governingLaw": "Governing Law", "contactUs": "Contact Us", "acceptanceContent": "By accessing and using our service, you agree to be bound by these terms of service. If you do not agree to these terms, please do not use our service.", "descriptionContent": "Shadcn Admin is an open-source React admin interface framework that provides component libraries, templates, and tools to help developers build modern admin applications.", "responsibilitiesContent": "Users agree to: use the service legally, not engage in any activities that may harm the service, protect account security, and comply with all applicable laws and regulations.", "propertyContent": "All content, features, and technology in the service are owned by us or our licensors and are protected by copyright, trademark, and other intellectual property laws.", "limitationContent": "To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, or consequential damages.", "terminationContent": "We reserve the right to suspend or terminate your access to the service at any time, especially in cases of violation of these terms.", "lawContent": "These terms are governed by the laws of the People's Republic of China, and any disputes will be resolved in the courts of our jurisdiction."}, "proTable": {"search": {"placeholder": "Enter search content", "searchConditions": "Search Conditions", "query": "Query", "reset": "Reset", "collapse": "Collapse", "expand": "Expand", "searchResultCount": "{count} search conditions set", "all": "All", "pleaseEnter": "Please enter {title}"}, "table": {"noData": "No Data", "loading": "Loading...", "searchEmpty": "No search results", "filterEmpty": "No filter results", "loadingFailed": "Failed to load data", "retry": "Retry"}, "pagination": {"total": "Total {total} pages", "showTotal": "Showing {start}-{end} of {total} items", "current": "Page {current}", "pageSize": "{pageSize} items per page", "page": "Page", "goto": "Go to", "prev": "Previous", "next": "Next", "first": "First", "last": "Last", "itemsPerPage": "Items per page"}, "toolbar": {"refresh": "Refresh", "settings": "<PERSON>umn <PERSON>", "columns": "Columns", "density": "Density", "fullscreen": "Fullscreen", "export": "Export", "add": "Add", "batchDelete": "<PERSON><PERSON> Delete", "import": "Import"}, "column": {"sort": "Sort", "sortAsc": "Sort Ascending", "sortDesc": "Sort Descending", "filter": "Filter", "hide": "<PERSON>de", "pin": "<PERSON>n", "pinLeft": "<PERSON>n to <PERSON>", "pinRight": "Pin to <PERSON>"}, "selection": {"selectAll": "Select All", "selectRow": "Select Row", "selectedItems": "{count} items selected", "clear": "Clear Selection"}, "error": {"loadFailed": "Load Failed", "networkError": "Network Error", "timeout": "Request Timeout", "unknown": "Unknown Error"}}}