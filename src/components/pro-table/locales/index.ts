// ProTable 国际化配置

export interface ProTableLocale {
  // 搜索相关
  search: {
    placeholder: string
    searchConditions: string
    query: string
    reset: string
    collapse: string
    expand: string
    searchResultCount: string
    all: string
  }
  
  // 表格相关
  table: {
    noData: string
    loading: string
    searchEmpty: string
    filterEmpty: string
    loadingFailed: string
    retry: string
  }
  
  // 分页相关
  pagination: {
    total: string
    current: string
    pageSize: string
    page: string
    goto: string
    prev: string
    next: string
    first: string
    last: string
    itemsPerPage: string
  }
  
  // 工具栏相关
  toolbar: {
    refresh: string
    settings: string
    columns: string
    density: string
    fullscreen: string
    export: string
  }
  
  // 列操作相关
  column: {
    sort: string
    sortAsc: string
    sortDesc: string
    filter: string
    hide: string
    pin: string
    pinLeft: string
    pinRight: string
  }
  
  // 行选择相关
  selection: {
    selectAll: string
    selectRow: string
    selectedItems: string
    clear: string
  }
  
  // 错误信息
  error: {
    loadFailed: string
    networkError: string
    timeout: string
    unknown: string
  }
}

// 中文语言包
export const zhCN: ProTableLocale = {
  search: {
    placeholder: '请输入搜索内容',
    searchConditions: '搜索条件',
    query: '查询',
    reset: '重置',
    collapse: '收起',
    expand: '展开',
    searchResultCount: '已设置 {count} 个搜索条件',
    all: '全部',
  },
  table: {
    noData: '暂无数据',
    loading: '加载中...',
    searchEmpty: '搜索无结果',
    filterEmpty: '筛选无结果',
    loadingFailed: '数据加载失败',
    retry: '重试',
  },
  pagination: {
    total: '共 {total} 项',
    current: '第 {current} 页',
    pageSize: '每页 {pageSize} 项',
    page: '页',
    goto: '跳转到',
    prev: '上一页',
    next: '下一页',
    first: '第一页',
    last: '最后一页',
    itemsPerPage: '每页显示',
  },
  toolbar: {
    refresh: '刷新',
    settings: '列设置',
    columns: '显示列',
    density: '密度',
    fullscreen: '全屏',
    export: '导出',
  },
  column: {
    sort: '排序',
    sortAsc: '升序',
    sortDesc: '降序',
    filter: '筛选',
    hide: '隐藏',
    pin: '固定',
    pinLeft: '固定到左侧',
    pinRight: '固定到右侧',
  },
  selection: {
    selectAll: '全选',
    selectRow: '选择行',
    selectedItems: '已选择 {count} 项',
    clear: '清除选择',
  },
  error: {
    loadFailed: '加载失败',
    networkError: '网络错误',
    timeout: '请求超时',
    unknown: '未知错误',
  },
}

// 英文语言包
export const enUS: ProTableLocale = {
  search: {
    placeholder: 'Enter search content',
    searchConditions: 'Search Conditions',
    query: 'Query',
    reset: 'Reset',
    collapse: 'Collapse',
    expand: 'Expand',
    searchResultCount: '{count} search conditions set',
    all: 'All',
  },
  table: {
    noData: 'No Data',
    loading: 'Loading...',
    searchEmpty: 'No search results',
    filterEmpty: 'No filter results',
    loadingFailed: 'Failed to load data',
    retry: 'Retry',
  },
  pagination: {
    total: 'Total {total} items',
    current: 'Page {current}',
    pageSize: '{pageSize} items per page',
    page: 'Page',
    goto: 'Go to',
    prev: 'Previous',
    next: 'Next',
    first: 'First',
    last: 'Last',
    itemsPerPage: 'Items per page',
  },
  toolbar: {
    refresh: 'Refresh',
    settings: 'Column Settings',
    columns: 'Columns',
    density: 'Density',
    fullscreen: 'Fullscreen',
    export: 'Export',
  },
  column: {
    sort: 'Sort',
    sortAsc: 'Sort Ascending',
    sortDesc: 'Sort Descending',
    filter: 'Filter',
    hide: 'Hide',
    pin: 'Pin',
    pinLeft: 'Pin to Left',
    pinRight: 'Pin to Right',
  },
  selection: {
    selectAll: 'Select All',
    selectRow: 'Select Row',
    selectedItems: '{count} items selected',
    clear: 'Clear Selection',
  },
  error: {
    loadFailed: 'Load Failed',
    networkError: 'Network Error',
    timeout: 'Request Timeout',
    unknown: 'Unknown Error',
  },
}

// 默认语言包
export const defaultLocale = zhCN

// 格式化文本函数
export function formatMessage(template: string, values: Record<string, any> = {}): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return values[key] !== undefined ? String(values[key]) : match
  })
}
