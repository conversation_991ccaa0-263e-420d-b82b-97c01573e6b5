/* ProTable 移动端优化样式 */

/* 表格容器宽度控制 */
.pro-table-container {
  /* 确保容器不会超出父元素宽度 */
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 移动端表格滚动优化 */
@media (max-width: 768px) {
  .pro-table-container {
    /* 确保表格在移动端可以水平滚动 */
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    /* 防止容器溢出 */
    width: 100%;
    max-width: 100%;
  }

  .pro-table-container > div {
    /* 内层滚动容器 */
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .pro-table-container table {
    /* 防止表格在移动端被压缩，但不超出容器 */
    min-width: 600px;
    width: max-content;
  }

  /* 移动端分页优化 */
  .pro-table-pagination {
    /* 分页容器在移动端的优化 */
    padding: 0.75rem 0.5rem;
  }

  .pro-table-pagination button {
    /* 分页按钮触摸区域优化 */
    min-height: 36px;
    min-width: 36px;
  }

  /* 移动端搜索表单优化 */
  .pro-table-search-mobile {
    /* 搜索表单在移动端的间距调整 */
    padding: 1rem;
    gap: 1rem;
  }

  .pro-table-search-mobile .form-item {
    /* 移动端表单项全宽显示 */
    width: 100%;
  }

  /* 移动端工具栏优化 */
  .pro-table-toolbar-mobile {
    /* 工具栏在移动端的布局调整 */
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .pro-table-toolbar-mobile .toolbar-actions {
    /* 工具栏按钮在移动端的布局 */
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* 移动端表格单元格优化 */
  .pro-table-cell-mobile {
    /* 移动端单元格的最小宽度和内边距 */
    min-width: 120px;
    padding: 0.75rem 0.5rem;
  }

  /* 移动端操作列优化 */
  .pro-table-actions-mobile {
    /* 操作按钮在移动端的布局 */
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 80px;
  }

  .pro-table-actions-mobile button {
    /* 操作按钮的最小触摸区域 */
    min-height: 36px;
    font-size: 0.875rem;
  }
}

/* 超小屏幕优化 (< 480px) */
@media (max-width: 480px) {
  .pro-table-container {
    /* 超小屏幕的边距调整 */
    margin: 0 -0.5rem;
    border-radius: 0.375rem;
  }

  .pro-table-pagination {
    /* 超小屏幕的分页容器 */
    padding: 0.5rem 0.25rem;
  }

  .pro-table-pagination button {
    /* 超小屏幕的分页按钮 */
    min-height: 40px;
    min-width: 40px;
  }

  /* 超小屏幕的搜索表单 */
  .pro-table-search-mobile {
    padding: 0.75rem;
  }

  /* 超小屏幕的表格单元格 */
  .pro-table-cell-mobile {
    min-width: 100px;
    padding: 0.5rem 0.25rem;
    font-size: 0.875rem;
  }
}

/* 中等屏幕优化 (480px - 768px) */
@media (min-width: 480px) and (max-width: 768px) {
  .pro-table-pagination {
    padding: 1rem 0.75rem;
  }

  .pro-table-pagination button {
    min-height: 32px;
    min-width: 32px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .pro-table-container {
    /* 横屏模式下的高度优化 */
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }

  .pro-table-search-mobile {
    /* 横屏模式下搜索表单的紧凑布局 */
    padding: 0.5rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 触摸设备的按钮优化 */
  .pro-table button,
  .pro-table .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* 触摸设备的选择框优化 */
  .pro-table select,
  .pro-table input[type="text"],
  .pro-table input[type="number"] {
    min-height: 44px;
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .pro-table {
    border-width: 2px;
  }

  .pro-table button {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .pro-table * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .pro-table-container {
    /* 深色模式下的边框和背景优化 */
    border-color: hsl(var(--border));
    background-color: hsl(var(--background));
  }
}

/* 打印样式优化 */
@media print {
  .pro-table-pagination,
  .pro-table-toolbar,
  .pro-table-search {
    display: none !important;
  }

  .pro-table-container {
    overflow: visible !important;
    border: 1px solid #000;
  }

  .pro-table table {
    min-width: auto !important;
    width: 100% !important;
  }
}
