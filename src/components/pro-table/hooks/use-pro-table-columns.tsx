import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { ProColumnType } from '../types'
import { ProTableColumnHeader } from '../components/pro-table-column-header'
import { ProTableActions } from '../components/pro-table-actions'
import { renderValueType } from '../utils/render-value-type'

interface UseProTableColumnsProps<T> {
  columns: ProColumnType<T>[]
  rowSelection?: boolean | {
    type?: 'checkbox' | 'radio'
    selectedRowKeys?: React.Key[]
    onChange?: (selectedRowKeys: React.Key[], selectedRows: T[]) => void
    getCheckboxProps?: (record: T) => { disabled?: boolean, name?: string }
  }
  onRowSelectionChange?: (updater: any) => void
}

export function useProTableColumns<T extends Record<string, any>>({
  columns: proColumns,
  rowSelection,
  onRowSelectionChange,
}: UseProTableColumnsProps<T>): ColumnDef<T>[] {
  return useMemo(() => {
    const columns: ColumnDef<T>[] = []

    // 添加选择列
    if (rowSelection) {
      const selectionType = typeof rowSelection === 'object' ? rowSelection.type : 'checkbox'
      
      columns.push({
        id: 'select',
        header: selectionType === 'checkbox' ? ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="全选"
            className="translate-y-[2px]"
          />
        ) : undefined,
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="选择行"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      })
    }

    // 转换 ProColumn 到 ColumnDef
    proColumns.forEach((proColumn) => {
      if (proColumn.hideInTable) return

      const {
        title,
        dataIndex,
        key,
        width,
        valueType = 'text',
        valueEnum,
        render,
        sorter,
        columnDef = {},
      } = proColumn

      const column: ColumnDef<T> = {
        id: key || (dataIndex as string),
        accessorKey: dataIndex as string,
        header: ({ column }) => (
          <ProTableColumnHeader 
            column={column} 
            title={title as string} 
            sorter={sorter}
          />
        ),
        cell: ({ row, getValue }) => {
          const value = getValue()
          const record = row.original
          const index = row.index

          // 自定义渲染优先
          if (render) {
            return render(value, record, index)
          }

          // 操作列
          if (valueType === 'actions') {
            return <ProTableActions record={record} />
          }

          // 根据 valueType 渲染
          return renderValueType(value, valueType, { valueEnum, record })
        },
        enableSorting: !!sorter,
        sortingFn: typeof sorter === 'function' ? (rowA, rowB) => {
          return sorter(rowA.original, rowB.original)
        } : undefined,
        size: typeof width === 'number' ? width : undefined,
        ...columnDef,
      }

      columns.push(column)
    })

    return columns
  }, [proColumns, rowSelection, onRowSelectionChange])
}

export default useProTableColumns
