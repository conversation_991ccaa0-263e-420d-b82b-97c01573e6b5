import { useMemo } from 'react'
import { ProTableLocale, defaultLocale, formatMessage } from '../locales'

interface UseProTableLocaleProps {
  locale?: ProTableLocale
}

export function useProTableLocale({ locale }: UseProTableLocaleProps = {}) {
  const currentLocale = locale || defaultLocale

  // 创建格式化函数
  const t = useMemo(() => {
    return {
      // 搜索相关
      search: {
        placeholder: (title?: string) => 
          title ? `请输入${title}` : currentLocale.search.placeholder,
        searchConditions: () => currentLocale.search.searchConditions,
        query: () => currentLocale.search.query,
        reset: () => currentLocale.search.reset,
        collapse: () => currentLocale.search.collapse,
        expand: () => currentLocale.search.expand,
        searchResultCount: (count: number) => 
          formatMessage(currentLocale.search.searchResultCount, { count }),
        all: () => currentLocale.search.all,
      },
      
      // 表格相关
      table: {
        noData: () => currentLocale.table.noData,
        loading: () => currentLocale.table.loading,
        searchEmpty: () => currentLocale.table.searchEmpty,
        filterEmpty: () => currentLocale.table.filterEmpty,
        loadingFailed: (error?: string) => 
          error ? `${currentLocale.table.loadingFailed}: ${error}` : currentLocale.table.loadingFailed,
        retry: () => currentLocale.table.retry,
      },
      
      // 分页相关
      pagination: {
        total: (total: number, range?: [number, number]) => {
          if (range) {
            return `显示 ${range[0]}-${range[1]} 项，共 ${total} 项`
          }
          return formatMessage(currentLocale.pagination.total, { total })
        },
        current: (current: number) => 
          formatMessage(currentLocale.pagination.current, { current }),
        pageSize: (pageSize: number) => 
          formatMessage(currentLocale.pagination.pageSize, { pageSize }),
        page: () => currentLocale.pagination.page,
        goto: () => currentLocale.pagination.goto,
        prev: () => currentLocale.pagination.prev,
        next: () => currentLocale.pagination.next,
        first: () => currentLocale.pagination.first,
        last: () => currentLocale.pagination.last,
        itemsPerPage: () => currentLocale.pagination.itemsPerPage,
      },
      
      // 工具栏相关
      toolbar: {
        refresh: () => currentLocale.toolbar.refresh,
        settings: () => currentLocale.toolbar.settings,
        columns: () => currentLocale.toolbar.columns,
        density: () => currentLocale.toolbar.density,
        fullscreen: () => currentLocale.toolbar.fullscreen,
        export: () => currentLocale.toolbar.export,
      },
      
      // 列操作相关
      column: {
        sort: () => currentLocale.column.sort,
        sortAsc: () => currentLocale.column.sortAsc,
        sortDesc: () => currentLocale.column.sortDesc,
        filter: () => currentLocale.column.filter,
        hide: () => currentLocale.column.hide,
        pin: () => currentLocale.column.pin,
        pinLeft: () => currentLocale.column.pinLeft,
        pinRight: () => currentLocale.column.pinRight,
      },
      
      // 行选择相关
      selection: {
        selectAll: () => currentLocale.selection.selectAll,
        selectRow: () => currentLocale.selection.selectRow,
        selectedItems: (count: number) => 
          formatMessage(currentLocale.selection.selectedItems, { count }),
        clear: () => currentLocale.selection.clear,
      },
      
      // 错误信息
      error: {
        loadFailed: () => currentLocale.error.loadFailed,
        networkError: () => currentLocale.error.networkError,
        timeout: () => currentLocale.error.timeout,
        unknown: () => currentLocale.error.unknown,
      },
    }
  }, [currentLocale])

  return { t, locale: currentLocale }
}
