import { useState, useEffect, useCallback } from 'react'
import { PaginationConfig } from '../types'

interface UseProTableDataProps<T> {
  dataSource?: T[]
  request?: (params: {
    current?: number
    pageSize?: number
    [key: string]: any
  }) => Promise<{ data: T[], total?: number, success?: boolean }>
  pagination?: PaginationConfig
}

interface UseProTableDataReturn<T> {
  data: T[]
  loading: boolean
  total: number
  current: number
  pageSize: number
  reload: () => void
  setParams: (params: any) => void
}

export function useProTableData<T>({
  dataSource,
  request,
  pagination,
}: UseProTableDataProps<T>): UseProTableDataReturn<T> {
  const [data, setData] = useState<T[]>(dataSource || [])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [current, setCurrent] = useState(pagination?.current || 1)
  const [pageSize, setPageSize] = useState(pagination?.pageSize || 10)
  const [params, setParams] = useState<any>({})

  // 加载数据
  const loadData = useCallback(async (requestParams?: any) => {
    if (!request) {
      // 使用本地数据源
      if (dataSource) {
        setData(dataSource)
        setTotal(dataSource.length)
      }
      return
    }

    setLoading(true)
    try {
      const finalParams = {
        current,
        pageSize,
        ...params,
        ...requestParams,
      }

      const response = await request(finalParams)
      
      if (response.success !== false) {
        setData(response.data || [])
        setTotal(response.total || response.data?.length || 0)
        
        // 更新分页状态
        if (finalParams.current) setCurrent(finalParams.current)
        if (finalParams.pageSize) setPageSize(finalParams.pageSize)
      }
    } catch (error) {
      console.error('ProTable 数据加载失败:', error)
      setData([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }, [request, current, pageSize, params, dataSource])

  // 重新加载
  const reload = useCallback(() => {
    loadData()
  }, [loadData])

  // 设置参数并重新加载
  const handleSetParams = useCallback((newParams: any) => {
    setParams((prev: any) => ({ ...prev, ...newParams }))
    loadData(newParams)
  }, [loadData])

  // 初始加载
  useEffect(() => {
    loadData()
  }, []) // 只在组件挂载时执行一次

  // 监听 dataSource 变化
  useEffect(() => {
    if (dataSource && !request) {
      setData(dataSource)
      setTotal(dataSource.length)
    }
  }, [dataSource, request])

  return {
    data,
    loading,
    total,
    current,
    pageSize,
    reload,
    setParams: handleSetParams,
  }
}
