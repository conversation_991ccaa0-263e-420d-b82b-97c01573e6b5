import { useState, useEffect, useCallback, useRef } from 'react'
import { PaginationConfig } from '../types'

interface UseProTableDataProps<T> {
  dataSource?: T[]
  request?: (params: {
    current?: number
    pageSize?: number
    [key: string]: unknown
  }) => Promise<{ data: T[], total?: number, success?: boolean }>
  pagination?: PaginationConfig
  onError?: (error: Error) => void
  retryCount?: number
  retryDelay?: number
}

interface UseProTableDataReturn<T> {
  data: T[]
  loading: boolean
  error: Error | null
  total: number
  current: number
  pageSize: number
  reload: () => void
  setParams: (params: Record<string, unknown>) => void
  retry: () => void
}

export function useProTableData<T>({
  dataSource,
  request,
  pagination,
  onError,
  retryCount = 3,
  retryDelay = 1000,
}: UseProTableDataProps<T>): UseProTableDataReturn<T> {
  const [data, setData] = useState<T[]>(dataSource || [])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [total, setTotal] = useState(0)
  const [current, setCurrent] = useState(pagination?.current || 1)
  const [pageSize, setPageSize] = useState(pagination?.pageSize || 10)
  const [params, setParams] = useState<Record<string, unknown>>({})

  // 使用 ref 来跟踪当前请求，避免竞态条件
  const currentRequestRef = useRef<number>(0)
  const retryCountRef = useRef<number>(0)

  // 加载数据
  const loadData = useCallback(async (requestParams?: Record<string, unknown>, isRetry = false) => {
    if (!request) {
      // 使用本地数据源
      if (dataSource) {
        setData(dataSource)
        setTotal(dataSource.length)
        setError(null)
      }
      return
    }

    // 增加请求计数器，用于避免竞态条件
    const requestId = ++currentRequestRef.current

    if (!isRetry) {
      retryCountRef.current = 0
    }

    setLoading(true)
    setError(null)

    try {
      const finalParams = {
        current,
        pageSize,
        ...params,
        ...requestParams,
      }

      const response = await request(finalParams)

      // 检查是否是最新的请求
      if (requestId !== currentRequestRef.current) {
        return // 忽略过期的请求
      }

      if (response.success !== false) {
        setData(response.data || [])
        setTotal(response.total || response.data?.length || 0)
        setError(null)
        retryCountRef.current = 0

        // 更新分页状态
        if (typeof finalParams.current === 'number') setCurrent(finalParams.current)
        if (typeof finalParams.pageSize === 'number') setPageSize(finalParams.pageSize)
      } else {
        throw new Error('请求失败')
      }
    } catch (err) {
      // 检查是否是最新的请求
      if (requestId !== currentRequestRef.current) {
        return
      }

      const error = err instanceof Error ? err : new Error('未知错误')

      // 如果还有重试次数，则自动重试
      if (retryCountRef.current < retryCount) {
        retryCountRef.current++
        setTimeout(() => {
          loadData(requestParams, true)
        }, retryDelay * retryCountRef.current) // 指数退避
        return
      }

      setError(error)
      setData([])
      setTotal(0)
      onError?.(error)
    } finally {
      // 只有最新的请求才能设置 loading 状态
      if (requestId === currentRequestRef.current) {
        setLoading(false)
      }
    }
  }, [request, current, pageSize, params, dataSource, retryCount, retryDelay, onError])

  // 重新加载
  const reload = useCallback(() => {
    loadData()
  }, [loadData])

  // 手动重试
  const retry = useCallback(() => {
    retryCountRef.current = 0
    loadData()
  }, [loadData])

  // 设置参数并重新加载
  const handleSetParams = useCallback((newParams: Record<string, unknown>) => {
    setParams((prev) => ({ ...prev, ...newParams }))
    loadData(newParams)
  }, [loadData])

  // 初始加载
  useEffect(() => {
    loadData()
  }, []) // 只在组件挂载时执行一次

  // 监听 dataSource 变化
  useEffect(() => {
    if (dataSource && !request) {
      setData(dataSource)
      setTotal(dataSource.length)
      setError(null)
    }
  }, [dataSource, request])

  return {
    data,
    loading,
    error,
    total,
    current,
    pageSize,
    reload,
    setParams: handleSetParams,
    retry,
  }
}
