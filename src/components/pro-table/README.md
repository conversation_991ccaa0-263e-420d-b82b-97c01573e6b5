# ProTable - 高级表格组件

基于 @tanstack/react-table 和 shadcn/ui 组件库封装的高级表格组件，参考 Ant Design Pro Components 的 ProTable 设计。

## 特性

- 🚀 **简化使用**：通过配置式 API 减少代码量
- 🔍 **内置搜索**：支持全局搜索和列筛选
- 📊 **智能排序**：支持多列排序
- 📄 **分页功能**：内置分页组件
- 🎛️ **工具栏**：可配置的工具栏和操作按钮
- 🎨 **类型安全**：完整的 TypeScript 支持
- 🔧 **高度可定制**：支持自定义渲染和扩展

## 基础用法

```tsx
import ProTable, { ProColumnType } from '@/components/pro-table'

interface DataType {
  id: string
  name: string
  status: string
  createdAt: Date
}

const columns: ProColumnType<DataType>[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
    search: false,
  },
  {
    title: '名称',
    dataIndex: 'name',
    search: {
      placeholder: '搜索名称...',
    },
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    valueType: 'select',
    valueEnum: {
      active: '激活',
      inactive: '未激活',
    },
    filters: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    valueType: 'dateTime',
    sorter: true,
  },
  {
    title: '操作',
    key: 'actions',
    valueType: 'actions',
    width: 120,
  },
]

function MyTable() {
  return (
    <ProTable<DataType>
      columns={columns}
      dataSource={data}
      rowKey="id"
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
      }}
      rowSelection={{
        type: 'checkbox',
      }}
    />
  )
}
```

## 列配置 (ProColumnType)

### 基础配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| title | 列标题 | ReactNode | - |
| dataIndex | 数据字段名 | keyof T \| string | - |
| key | 列的唯一标识 | string | - |
| width | 列宽度 | number \| string | - |
| fixed | 固定列 | 'left' \| 'right' | - |

### 值类型和渲染

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| valueType | 值类型 | ValueType | 'text' |
| valueEnum | 枚举值配置 | Record<string, OptionConfig \| string> | - |
| render | 自定义渲染 | (value, record, index) => ReactNode | - |

### 搜索和筛选

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| search | 是否支持搜索 | boolean \| SearchConfig | true |
| filters | 是否支持筛选 | boolean \| OptionConfig[] | false |

### 排序

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| sorter | 是否支持排序 | boolean \| ((a, b) => number) | false |

## 支持的值类型 (ValueType)

- `text` - 文本
- `select` - 选择器
- `date` - 日期
- `dateTime` - 日期时间
- `dateRange` - 日期范围
- `number` - 数字
- `money` - 金额
- `percent` - 百分比
- `option` - 选项
- `actions` - 操作列

## 远程数据

```tsx
const request = async (params: any) => {
  const response = await fetch('/api/data', {
    method: 'POST',
    body: JSON.stringify(params),
  })
  const result = await response.json()
  
  return {
    data: result.list,
    total: result.total,
    success: true,
  }
}

<ProTable
  columns={columns}
  request={request}
  pagination={{
    pageSize: 20,
  }}
/>
```

## 工具栏配置

```tsx
<ProTable
  columns={columns}
  dataSource={data}
  toolbar={{
    search: {
      placeholder: '全局搜索...',
    },
    refresh: true,
    setting: true,
    actions: [
      <Button key="add">新增</Button>,
      <Button key="export">导出</Button>,
    ],
  }}
/>
```

## 行选择

```tsx
<ProTable
  columns={columns}
  dataSource={data}
  rowSelection={{
    type: 'checkbox',
    selectedRowKeys: selectedKeys,
    onChange: (keys, rows) => {
      setSelectedKeys(keys)
      setSelectedRows(rows)
    },
  }}
/>
```

## 操作引用

```tsx
const actionRef = useRef<ProTableActionType>()

const handleReload = () => {
  actionRef.current?.reload()
}

const handleReset = () => {
  actionRef.current?.reset()
}

<ProTable
  actionRef={actionRef}
  columns={columns}
  dataSource={data}
/>
```

## 迁移指南

### 从原有 DataTable 迁移

**原有代码：**
```tsx
// 需要定义多个组件
<DataTable data={data} columns={columns} />
<DataTableToolbar table={table} />
<DataTablePagination table={table} />
```

**使用 ProTable：**
```tsx
// 一个组件搞定
<ProTable 
  columns={proColumns} 
  dataSource={data}
  search={true}
  pagination={{ pageSize: 10 }}
/>
```

### 列定义迁移

**原有 ColumnDef：**
```tsx
{
  accessorKey: 'status',
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title="Status" />
  ),
  cell: ({ row }) => {
    const status = statuses.find(s => s.value === row.getValue('status'))
    return status ? <Badge>{status.label}</Badge> : null
  },
  filterFn: (row, id, value) => value.includes(row.getValue(id)),
}
```

**ProTable 列配置：**
```tsx
{
  title: '状态',
  dataIndex: 'status',
  valueType: 'select',
  valueEnum: {
    active: '激活',
    inactive: '未激活',
  },
  filters: true,
}
```

## 完整示例

查看完整的演示页面：`/dashboard/pro-table-demo`

```tsx
// 访问演示页面
import { Link } from '@tanstack/react-router'

<Link to="/dashboard/pro-table-demo">
  查看 ProTable 演示
</Link>
```

## API 参考

### ProTable Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| columns | 列配置 | ProColumnType[] | - |
| dataSource | 本地数据源 | T[] | - |
| request | 远程数据请求函数 | (params) => Promise<{data, total, success}> | - |
| loading | 加载状态 | boolean | false |
| rowKey | 行唯一标识 | keyof T \| (record) => string | 'id' |
| search | 搜索配置 | boolean \| SearchConfig | true |
| toolbar | 工具栏配置 | ToolBarConfig | {} |
| pagination | 分页配置 | false \| PaginationConfig | {} |
| rowSelection | 行选择配置 | boolean \| RowSelectionConfig | - |
| actionRef | 表格操作引用 | MutableRefObject | - |

### ProColumnType Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| title | 列标题 | ReactNode | - |
| dataIndex | 数据字段 | keyof T | - |
| key | 列唯一标识 | string | - |
| width | 列宽度 | number \| string | - |
| valueType | 值类型 | ValueType | 'text' |
| valueEnum | 枚举值 | Record<string, string \| OptionConfig> | - |
| render | 自定义渲染 | (value, record, index) => ReactNode | - |
| search | 搜索配置 | boolean \| SearchConfig | true |
| filters | 筛选配置 | boolean \| OptionConfig[] | false |
| sorter | 排序配置 | boolean \| (a, b) => number | false |
| fixed | 固定列 | 'left' \| 'right' | - |
| hideInTable | 在表格中隐藏 | boolean | false |
| hideInSearch | 在搜索中隐藏 | boolean | false |

## 性能优化

1. **虚拟滚动**：对于大量数据，考虑使用虚拟滚动
2. **分页加载**：使用服务端分页减少数据传输
3. **列宽优化**：合理设置列宽避免不必要的重排
4. **搜索防抖**：在搜索输入中使用防抖减少请求频率

## 最佳实践

1. **合理使用 valueType**：根据数据类型选择合适的 valueType
2. **配置搜索和筛选**：为常用查询字段启用搜索和筛选
3. **控制列宽度**：为重要列设置合适的宽度
4. **使用 actionRef**：通过 actionRef 控制表格行为
5. **自定义渲染**：对于复杂显示需求使用 render 函数
6. **类型安全**：为数据类型添加 `extends Record<string, unknown>` 约束
7. **响应式设计**：使用 scroll 属性处理小屏幕显示

## 故障排除

### 常见问题

**Q: 表格数据不显示？**
A: 检查 `dataIndex` 是否正确，确保数据结构匹配列配置。

**Q: 搜索功能不工作？**
A: 确保列配置中启用了 `search` 属性，并且 `hideInSearch` 不为 true。

**Q: 分页不正确？**
A: 检查 `request` 函数返回的 `total` 字段是否正确。

**Q: 类型错误？**
A: 确保数据类型继承 `Record<string, unknown>`。

### 调试技巧

1. 使用浏览器开发者工具查看网络请求
2. 检查 console 中的错误信息
3. 验证数据结构和类型定义
4. 使用 `actionRef` 获取表格状态进行调试
