# ProTable - 高级表格组件

基于 @tanstack/react-table 和 shadcn/ui 组件库封装的高级表格组件，参考 Ant Design Pro Components 的 ProTable 设计。

## 特性

- 🚀 **简化使用**：通过配置式 API 减少代码量
- 🔍 **内置搜索**：支持全局搜索和列筛选
- 📊 **智能排序**：支持多列排序
- 📄 **分页功能**：内置分页组件
- 🎛️ **工具栏**：可配置的工具栏和操作按钮
- 🎨 **类型安全**：完整的 TypeScript 支持
- 🔧 **高度可定制**：支持自定义渲染和扩展

## 基础用法

```tsx
import ProTable, { ProColumnType } from '@/components/pro-table'

interface DataType {
  id: string
  name: string
  status: string
  createdAt: Date
}

const columns: ProColumnType<DataType>[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
    search: false,
  },
  {
    title: '名称',
    dataIndex: 'name',
    search: {
      placeholder: '搜索名称...',
    },
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    valueType: 'select',
    valueEnum: {
      active: '激活',
      inactive: '未激活',
    },
    filters: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    valueType: 'dateTime',
    sorter: true,
  },
  {
    title: '操作',
    key: 'actions',
    valueType: 'actions',
    width: 120,
  },
]

function MyTable() {
  return (
    <ProTable<DataType>
      columns={columns}
      dataSource={data}
      rowKey="id"
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
      }}
      rowSelection={{
        type: 'checkbox',
      }}
    />
  )
}
```

## 列配置 (ProColumnType)

### 基础配置

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| title | 列标题 | ReactNode | - |
| dataIndex | 数据字段名 | keyof T \| string | - |
| key | 列的唯一标识 | string | - |
| width | 列宽度 | number \| string | - |
| fixed | 固定列 | 'left' \| 'right' | - |

### 值类型和渲染

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| valueType | 值类型 | ValueType | 'text' |
| valueEnum | 枚举值配置 | Record<string, OptionConfig \| string> | - |
| render | 自定义渲染 | (value, record, index) => ReactNode | - |

### 搜索和筛选

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| search | 是否支持搜索 | boolean \| SearchConfig | true |
| filters | 是否支持筛选 | boolean \| OptionConfig[] | false |

### 排序

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| sorter | 是否支持排序 | boolean \| ((a, b) => number) | false |

## 支持的值类型 (ValueType)

- `text` - 文本
- `select` - 选择器
- `date` - 日期
- `dateTime` - 日期时间
- `dateRange` - 日期范围
- `number` - 数字
- `money` - 金额
- `percent` - 百分比
- `option` - 选项
- `actions` - 操作列

## 远程数据

```tsx
const request = async (params: any) => {
  const response = await fetch('/api/data', {
    method: 'POST',
    body: JSON.stringify(params),
  })
  const result = await response.json()
  
  return {
    data: result.list,
    total: result.total,
    success: true,
  }
}

<ProTable
  columns={columns}
  request={request}
  pagination={{
    pageSize: 20,
  }}
/>
```

## 工具栏配置

```tsx
<ProTable
  columns={columns}
  dataSource={data}
  toolbar={{
    search: {
      placeholder: '全局搜索...',
    },
    refresh: true,
    setting: true,
    actions: [
      <Button key="add">新增</Button>,
      <Button key="export">导出</Button>,
    ],
  }}
/>
```

## 行选择

```tsx
<ProTable
  columns={columns}
  dataSource={data}
  rowSelection={{
    type: 'checkbox',
    selectedRowKeys: selectedKeys,
    onChange: (keys, rows) => {
      setSelectedKeys(keys)
      setSelectedRows(rows)
    },
  }}
/>
```

## 操作引用

```tsx
const actionRef = useRef<ProTableActionType>()

const handleReload = () => {
  actionRef.current?.reload()
}

const handleReset = () => {
  actionRef.current?.reset()
}

<ProTable
  actionRef={actionRef}
  columns={columns}
  dataSource={data}
/>
```

## 迁移指南

### 从原有 DataTable 迁移

**原有代码：**
```tsx
// 需要定义多个组件
<DataTable data={data} columns={columns} />
<DataTableToolbar table={table} />
<DataTablePagination table={table} />
```

**使用 ProTable：**
```tsx
// 一个组件搞定
<ProTable 
  columns={proColumns} 
  dataSource={data}
  search={true}
  pagination={{ pageSize: 10 }}
/>
```

### 列定义迁移

**原有 ColumnDef：**
```tsx
{
  accessorKey: 'status',
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title="Status" />
  ),
  cell: ({ row }) => {
    const status = statuses.find(s => s.value === row.getValue('status'))
    return status ? <Badge>{status.label}</Badge> : null
  },
  filterFn: (row, id, value) => value.includes(row.getValue(id)),
}
```

**ProTable 列配置：**
```tsx
{
  title: '状态',
  dataIndex: 'status',
  valueType: 'select',
  valueEnum: {
    active: '激活',
    inactive: '未激活',
  },
  filters: true,
}
```

## 最佳实践

1. **合理使用 valueType**：根据数据类型选择合适的 valueType
2. **配置搜索和筛选**：为常用查询字段启用搜索和筛选
3. **控制列宽度**：为重要列设置合适的宽度
4. **使用 actionRef**：通过 actionRef 控制表格行为
5. **自定义渲染**：对于复杂显示需求使用 render 函数
