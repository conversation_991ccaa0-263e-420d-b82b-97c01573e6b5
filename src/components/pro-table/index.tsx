import { useCallback, useEffect, useImperativeH<PERSON>le, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ProTableProps, ProTableActionType } from './types'
import { useProTableColumns } from './hooks/use-pro-table-columns'
import { useProTableData } from './hooks/use-pro-table-data'
import { ProTableToolbar } from './components/pro-table-toolbar'
import { ProTablePagination } from './components/pro-table-pagination'
import { ProTableSearch } from './components/pro-table-search'
import { ProTableErrorBoundary } from './components/pro-table-error-boundary'
import { ProTableLoading, ProTableContentLoading, ProTableEmpty } from './components/pro-table-loading'
import './styles/mobile.css'

function ProTable<T extends Record<string, unknown>>(props: ProTableProps<T>) {
  const { t } = useTranslation()
  const {
    columns: procolumns,
    dataSource,
    request,
    rowKey: _rowKey = 'id',
    loading: externalLoading,
    search = true,
    toolbar = {},
    pagination = {},
    rowSelection,
    className,
    style,
    onRow,
    onHeaderRow,
    scroll: _scroll,
    expandable: _expandable,
    actionRef,
  } = props

  // 状态管理
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelectionState, setRowSelectionState] = useState({})
  const [globalFilter, setGlobalFilter] = useState('')
  const [searchValues, setSearchValues] = useState<Record<string, unknown>>({})

  // 数据管理
  const {
    data,
    loading: dataLoading,
    error,
    total,
    current,
    pageSize,
    reload,
    setParams,
    retry,
  } = useProTableData({
    dataSource,
    request,
    pagination: pagination !== false ? pagination : undefined,
    onError: (error: Error) => {
      console.error(t('proTable.error.loadFailed'), error)
    },
    retryCount: 3,
    retryDelay: 1000,
  })

  const loading = externalLoading ?? dataLoading

  // 列配置转换
  const columns = useProTableColumns<T>({
    columns: procolumns,
    rowSelection,
    onRowSelectionChange: setRowSelectionState,
  })

  // 表格实例
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection: rowSelectionState,
      columnFilters,
      globalFilter,
      pagination: pagination !== false ? {
        pageIndex: (current || 1) - 1,
        pageSize: pageSize || 10,
      } : undefined,
    },
    enableRowSelection: !!rowSelection,
    onRowSelectionChange: setRowSelectionState,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: pagination !== false ? getPaginationRowModel() : undefined,
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: !!request,
    manualSorting: !!request,
    manualFiltering: !!request,
    pageCount: request && total ? Math.ceil(total / (pageSize || 10)) : undefined,
  })

  // 搜索处理函数
  const handleSearch = useCallback((values: Record<string, unknown>) => {
    setSearchValues(values)
    // 将搜索值转换为列筛选
    const filters: ColumnFiltersState = Object.entries(values)
      .filter(([, value]) => value !== '' && value !== null && value !== undefined)
      .map(([key, value]) => ({ id: key, value }))
    setColumnFilters(filters)
  }, [])

  const handleSearchReset = useCallback(() => {
    setSearchValues({})
    setColumnFilters([])
    setGlobalFilter('')
  }, [])

  // 暴露操作方法
  const actionMethods: ProTableActionType<T> = useMemo(() => ({
    reload: () => {
      reload()
    },
    reset: () => {
      // 重置所有状态
      setColumnFilters([])
      setGlobalFilter('')
      setSorting([])
      setRowSelectionState({})
      setSearchValues({})
      // 重新加载数据
      reload()
    },
    clearSelected: () => {
      setRowSelectionState({})
    },
    getDataSource: () => data,
    getSelected: () => {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map(row => row.original)
      const selectedKeys = Object.keys(rowSelectionState).filter(key =>
        rowSelectionState[key as keyof typeof rowSelectionState]
      )
      return { keys: selectedKeys, rows: selectedRows }
    },
    setLoading: (_loading: boolean) => {
      // 这里可以添加外部控制加载状态的逻辑
      // 暂时不支持外部控制加载状态
    },
    setDataSource: (_newData: T[]) => {
      // 对于本地数据源，可以更新数据
      if (!request && dataSource) {
        // 暂时不支持动态设置数据源
      }
    },
    // 新增方法
    getTableInstance: () => table,
    exportData: (format: 'csv' | 'json' = 'csv') => {
      const exportData = table.getFilteredRowModel().rows.map(row => row.original)
      if (format === 'json') {
        return JSON.stringify(exportData, null, 2)
      }
      // CSV 导出逻辑
      const headers = procolumns
        .filter(col => !col.hideInTable && col.dataIndex)
        .map(col => col.title || col.dataIndex)
        .join(',')
      const rows = exportData.map(row =>
        procolumns
          .filter(col => !col.hideInTable && col.dataIndex)
          .map(col => {
            const value = row[col.dataIndex as keyof T]
            return typeof value === 'string' ? `"${value}"` : String(value || '')
          })
          .join(',')
      ).join('\n')
      return `${headers}\n${rows}`
    },
    setSearchValues: (values: Record<string, unknown>) => {
      setSearchValues(values)
      handleSearch(values)
    },
  }), [data, reload, rowSelectionState, table, procolumns, request, dataSource, handleSearch])

  useImperativeHandle(actionRef, () => actionMethods, [actionMethods])

  // 使用 ref 来跟踪参数变化，避免无限循环
  const lastParamsRef = useRef<string>('')

  // 提取分页状态
  const paginationState = table.getState().pagination
  const pageIndex = paginationState?.pageIndex || 0
  const currentPageSize = paginationState?.pageSize || 10

  // 处理参数变化
  useEffect(() => {
    if (!request) return

    const params: Record<string, unknown> = {
      current: pageIndex + 1,
      pageSize: currentPageSize,
    }

    // 添加排序参数
    if (sorting.length > 0) {
      params.sorter = sorting.map(sort => ({
        field: sort.id,
        order: sort.desc ? 'desc' : 'asc',
      }))
    }

    // 添加筛选参数
    if (columnFilters.length > 0) {
      columnFilters.forEach(filter => {
        params[filter.id] = filter.value
      })
    }

    // 添加全局搜索参数
    if (globalFilter) {
      params.search = globalFilter
    }

    // 只有参数真正变化时才更新
    const paramsString = JSON.stringify(params)
    if (paramsString !== lastParamsRef.current) {
      lastParamsRef.current = paramsString
      setParams(params)
    }
  }, [request, sorting, columnFilters, globalFilter, pageIndex, currentPageSize, setParams])

  return (
    <div className={cn('pro-table space-y-4', className)} style={style}>
      {/* 高级搜索 */}
      {search && typeof search === 'object' && (
        <ProTableSearch
          columns={procolumns}
          onSearch={handleSearch}
          onReset={handleSearchReset}
          collapsed={search.collapsed}
          labelWidth={search.labelWidth}
        />
      )}

      {/* 工具栏 */}
      {(search || Object.keys(toolbar).length > 0) && (
        <ProTableToolbar
          table={table}
          columns={procolumns}
          search={search === true ? true : false} // 只有简单搜索时显示
          toolbar={toolbar}
          loading={loading}
          onReload={reload}
          globalFilter={globalFilter}
          onGlobalFilterChange={setGlobalFilter}
        />
      )}

      {/* 表格 */}
      <div className="pro-table-container w-full overflow-hidden rounded-md border">
        <div
          className="w-full overflow-x-auto overflow-y-hidden"
          style={{
            maxWidth: scroll?.x ? (typeof scroll.x === 'number' ? `${scroll.x}px` : scroll.x) : '100%',
            maxHeight: scroll?.y ? (typeof scroll.y === 'number' ? `${scroll.y}px` : scroll.y) : undefined,
          } as React.CSSProperties}
        >
          <Table>
            <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow 
                key={headerGroup.id}
                {...(onHeaderRow?.(procolumns, 0) || {})}
              >
                {headerGroup.headers.map((header) => (
                  <TableHead 
                    key={header.id} 
                    colSpan={header.colSpan}
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
            </TableHeader>
            <TableBody>
            {error ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <ProTableEmpty
                    type="loading-failed"
                    description={`${t('proTable.table.loadingFailed')}: ${error.message}`}
                    action={
                      <button
                        onClick={retry}
                        className="text-sm text-primary hover:underline"
                      >
                        {t('proTable.table.retry')}
                      </button>
                    }
                  />
                </TableCell>
              </TableRow>
            ) : loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24">
                  <ProTableContentLoading columns={columns.length} />
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  {...(onRow?.(row.original, index) || {})}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24">
                  <ProTableEmpty
                    type={
                      globalFilter || columnFilters.length > 0
                        ? globalFilter
                          ? 'search-empty'
                          : 'filter-empty'
                        : 'no-data'
                    }
                  />
                </TableCell>
              </TableRow>
            )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* 分页 */}
      {pagination !== false && (
        <ProTablePagination
          table={table}
          total={total}
          current={current}
          pageSize={pageSize}
          pagination={pagination}
          loading={loading}
          onPageChange={(page: number, size: number) => {
            if (request) {
              setParams({ current: page, pageSize: size })
            } else {
              table.setPageIndex(page - 1)
              table.setPageSize(size)
            }
          }}
        />
      )}
    </div>
  )
}

// 使用错误边界包装的 ProTable
const ProTableWithErrorBoundary = <T extends Record<string, unknown>>(props: ProTableProps<T>) => (
  <ProTableErrorBoundary>
    <ProTable {...props} />
  </ProTableErrorBoundary>
)

export default ProTableWithErrorBoundary
export { ProTable as ProTableCore } // 导出不带错误边界的核心组件
export type { ProTableProps, ProColumnType, ProTableActionType } from './types'
