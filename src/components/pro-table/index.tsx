import { useEffect, useImperative<PERSON>andle, useMemo, useState } from 'react'
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ProTableProps, ProTableActionType } from './types'
import { useProTableColumns } from './hooks/use-pro-table-columns'
import { useProTableData } from './hooks/use-pro-table-data'
import { ProTableToolbar } from './components/pro-table-toolbar'
import { ProTablePagination } from './components/pro-table-pagination'
import { ProTableSearch } from './components/pro-table-search'

function ProTable<T extends Record<string, unknown>>(props: ProTableProps<T>) {
  const {
    columns: procolumns,
    dataSource,
    request,
    rowKey: _rowKey = 'id',
    loading: externalLoading,
    search = true,
    toolbar = {},
    pagination = {},
    rowSelection,
    className,
    style,
    onRow,
    onHeaderRow,
    scroll: _scroll,
    expandable: _expandable,
    actionRef,
  } = props

  // 状态管理
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelectionState, setRowSelectionState] = useState({})
  const [globalFilter, setGlobalFilter] = useState('')
  const [searchValues, setSearchValues] = useState<Record<string, unknown>>({})

  // 数据管理
  const {
    data,
    loading: dataLoading,
    total,
    current,
    pageSize,
    reload,
    setParams,
  } = useProTableData({
    dataSource,
    request,
    pagination: pagination !== false ? pagination : undefined,
  })

  const loading = externalLoading ?? dataLoading

  // 列配置转换
  const columns = useProTableColumns<T>({
    columns: procolumns,
    rowSelection,
    onRowSelectionChange: setRowSelectionState,
  })

  // 表格实例
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection: rowSelectionState,
      columnFilters,
      globalFilter,
      pagination: pagination !== false ? {
        pageIndex: (current || 1) - 1,
        pageSize: pageSize || 10,
      } : undefined,
    },
    enableRowSelection: !!rowSelection,
    onRowSelectionChange: setRowSelectionState,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: pagination !== false ? getPaginationRowModel() : undefined,
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: !!request,
    manualSorting: !!request,
    manualFiltering: !!request,
    pageCount: request && total ? Math.ceil(total / (pageSize || 10)) : undefined,
  })

  // 暴露操作方法
  const actionMethods: ProTableActionType<T> = useMemo(() => ({
    reload,
    reset: () => {
      setColumnFilters([])
      setGlobalFilter('')
      setSorting([])
      setRowSelectionState({})
      reload()
    },
    clearSelected: () => setRowSelectionState({}),
    getDataSource: () => data,
    getSelected: () => {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map(row => row.original)
      const selectedKeys = Object.keys(rowSelectionState)
      return { keys: selectedKeys, rows: selectedRows }
    },
    setLoading: () => {}, // 由 useProTableData 管理
    setDataSource: () => {}, // 由 useProTableData 管理
  }), [data, reload, rowSelectionState, table])

  useImperativeHandle(actionRef, () => actionMethods, [actionMethods])

  // 搜索处理函数
  const handleSearch = (values: Record<string, unknown>) => {
    setSearchValues(values)
    // 将搜索值转换为列筛选
    const filters: ColumnFiltersState = Object.entries(values)
      .filter(([, value]) => value !== '' && value !== null && value !== undefined)
      .map(([key, value]) => ({ id: key, value }))
    setColumnFilters(filters)
  }

  const handleSearchReset = () => {
    setSearchValues({})
    setColumnFilters([])
    setGlobalFilter('')
  }

  // 处理参数变化
  useEffect(() => {
    if (request) {
      const paginationState = table.getState().pagination
      const params: Record<string, unknown> = {
        current: (paginationState?.pageIndex || 0) + 1,
        pageSize: paginationState?.pageSize || 10,
      }

      // 添加排序参数
      if (sorting.length > 0) {
        params.sorter = sorting.map(sort => ({
          field: sort.id,
          order: sort.desc ? 'desc' : 'asc',
        }))
      }

      // 添加筛选参数
      if (columnFilters.length > 0) {
        columnFilters.forEach(filter => {
          params[filter.id] = filter.value
        })
      }

      // 添加全局搜索参数
      if (globalFilter) {
        params.search = globalFilter
      }

      setParams(params)
    }
  }, [sorting, columnFilters, globalFilter, table, request, setParams])

  return (
    <div className={cn('space-y-4', className)} style={style}>
      {/* 高级搜索 */}
      {search && typeof search === 'object' && (
        <ProTableSearch
          columns={procolumns}
          onSearch={handleSearch}
          onReset={handleSearchReset}
          collapsed={search.collapsed}
          labelWidth={search.labelWidth}
        />
      )}

      {/* 工具栏 */}
      {(search || Object.keys(toolbar).length > 0) && (
        <ProTableToolbar
          table={table}
          columns={procolumns}
          search={search === true ? true : false} // 只有简单搜索时显示
          toolbar={toolbar}
          loading={loading}
          onReload={reload}
          globalFilter={globalFilter}
          onGlobalFilterChange={setGlobalFilter}
        />
      )}

      {/* 表格 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow 
                key={headerGroup.id}
                {...(onHeaderRow?.(procolumns, 0) || {})}
              >
                {headerGroup.headers.map((header) => (
                  <TableHead 
                    key={header.id} 
                    colSpan={header.colSpan}
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  {...(onRow?.(row.original, index) || {})}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {loading ? '加载中...' : '暂无数据'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      {pagination !== false && (
        <ProTablePagination
          table={table}
          total={total}
          current={current}
          pageSize={pageSize}
          pagination={pagination}
          loading={loading}
          onPageChange={(page: number, size: number) => {
            if (request) {
              setParams({ current: page, pageSize: size })
            } else {
              table.setPageIndex(page - 1)
              table.setPageSize(size)
            }
          }}
        />
      )}
    </div>
  )
}

export default ProTable
export type { ProTableProps, ProColumnType, ProTableActionType } from './types'
