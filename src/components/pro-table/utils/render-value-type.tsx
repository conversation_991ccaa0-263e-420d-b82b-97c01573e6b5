import React from 'react'
import { Badge } from '@/components/ui/badge'
import { ValueType, OptionConfig } from '../types'

interface RenderOptions {
  valueEnum?: Record<string, OptionConfig | string>
  record?: any
}

export function renderValueType(
  value: any,
  valueType: ValueType,
  options: RenderOptions = {}
): React.ReactNode {
  const { valueEnum } = options

  if (value === null || value === undefined) {
    return '-'
  }

  switch (valueType) {
    case 'text':
      return <span>{String(value)}</span>

    case 'select':
    case 'option':
      if (valueEnum) {
        const option = valueEnum[value]
        if (typeof option === 'string') {
          return <Badge variant="outline">{option}</Badge>
        }
        if (typeof option === 'object') {
          const IconComponent = option.icon
          return (
            <div className="flex items-center gap-2">
              {IconComponent && <IconComponent className="h-4 w-4" />}
              <Badge 
                variant="outline" 
                className={option.color ? `border-${option.color}-500 text-${option.color}-700` : ''}
              >
                {option.label}
              </Badge>
            </div>
          )
        }
      }
      return <Badge variant="outline">{String(value)}</Badge>

    case 'date':
      if (value instanceof Date) {
        return <span>{value.toLocaleDateString()}</span>
      }
      if (typeof value === 'string' || typeof value === 'number') {
        const date = new Date(value)
        return <span>{date.toLocaleDateString()}</span>
      }
      return <span>{String(value)}</span>

    case 'dateTime':
      if (value instanceof Date) {
        return <span>{value.toLocaleString()}</span>
      }
      if (typeof value === 'string' || typeof value === 'number') {
        const date = new Date(value)
        return <span>{date.toLocaleString()}</span>
      }
      return <span>{String(value)}</span>

    case 'number':
      const num = Number(value)
      return <span>{isNaN(num) ? String(value) : num.toLocaleString()}</span>

    case 'money':
      const money = Number(value)
      return (
        <span>
          {isNaN(money) ? String(value) : `¥${money.toLocaleString()}`}
        </span>
      )

    case 'percent':
      const percent = Number(value)
      return (
        <span>
          {isNaN(percent) ? String(value) : `${(percent * 100).toFixed(2)}%`}
        </span>
      )

    case 'dateRange':
      if (Array.isArray(value) && value.length === 2) {
        const [start, end] = value
        const startDate = new Date(start)
        const endDate = new Date(end)
        return (
          <span>
            {startDate.toLocaleDateString()} ~ {endDate.toLocaleDateString()}
          </span>
        )
      }
      return <span>{String(value)}</span>

    default:
      return <span>{String(value)}</span>
  }
}

// 获取搜索组件
export function getSearchComponent(valueType: ValueType, options: RenderOptions = {}) {
  const { valueEnum } = options

  switch (valueType) {
    case 'select':
    case 'option':
      if (valueEnum && Object.keys(valueEnum).length > 0) {
        return {
          type: 'select',
          options: Object.entries(valueEnum)
            .filter(([value]) => value !== '' && value !== null && value !== undefined)
            .map(([value, option]) => ({
              value: value || 'empty',
              label: typeof option === 'string' ? option : (option?.label || value),
            })),
        }
      }
      return { type: 'input' }

    case 'date':
      return { type: 'date' }

    case 'dateTime':
      return { type: 'datetime' }

    case 'dateRange':
      return { type: 'dateRange' }

    case 'number':
    case 'money':
    case 'percent':
      return { type: 'number' }

    default:
      return { type: 'input' }
  }
}
