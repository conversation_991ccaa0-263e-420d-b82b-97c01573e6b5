import { ReactNode } from 'react'
import { ColumnDef, Table } from '@tanstack/react-table'

// 支持的值类型
export type ValueType = 
  | 'text'
  | 'select'
  | 'date'
  | 'dateTime'
  | 'dateRange'
  | 'number'
  | 'money'
  | 'percent'
  | 'option'
  | 'actions'

// 选项配置
export interface OptionConfig {
  label: string
  value?: string | number
  icon?: React.ComponentType<any>
  color?: string
}

// 列配置
export interface ProColumnType<T = any> {
  // 基础配置
  title?: ReactNode
  dataIndex?: keyof T | string
  key?: string
  width?: number | string
  fixed?: 'left' | 'right'
  
  // 值类型和渲染
  valueType?: ValueType
  valueEnum?: Record<string, OptionConfig | string>
  render?: (value: any, record: T, index: number) => ReactNode
  
  // 搜索和筛选
  search?: boolean | {
    placeholder?: string
    transform?: (value: any) => any
  }
  filters?: boolean | OptionConfig[]
  
  // 排序
  sorter?: boolean | ((a: T, b: T) => number)
  
  // 显示控制
  hideInTable?: boolean
  hideInSearch?: boolean
  
  // 其他配置
  copyable?: boolean
  ellipsis?: boolean
  tooltip?: string | ((record: T) => string)
  
  // 扩展 tanstack table 的列定义
  columnDef?: Partial<ColumnDef<T>>
}

// 工具栏配置
export interface ToolBarConfig<T = any> {
  search?: boolean | {
    placeholder?: string
    onSearch?: (value: string) => void
  }
  filter?: boolean
  refresh?: boolean | (() => void)
  density?: boolean
  fullScreen?: boolean
  setting?: boolean
  actions?: ReactNode[]
}

// 分页配置
export interface PaginationConfig {
  current?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean | ((total: number, range: [number, number]) => ReactNode)
  pageSizeOptions?: string[]
  onChange?: (page: number, pageSize: number) => void
}

// 请求配置
export interface RequestConfig<T = any> {
  url?: string
  method?: 'GET' | 'POST'
  params?: Record<string, any>
  transform?: (data: any) => { data: T[], total?: number, success?: boolean }
}

// ProTable 主要属性
export interface ProTableProps<T = any> {
  // 数据源
  dataSource?: T[]
  request?: (params: {
    current?: number
    pageSize?: number
    [key: string]: any
  }) => Promise<{ data: T[], total?: number, success?: boolean }>
  
  // 列配置
  columns: ProColumnType<T>[]
  
  // 表格配置
  rowKey?: keyof T | ((record: T) => string)
  loading?: boolean
  size?: 'small' | 'middle' | 'large'
  
  // 功能配置
  search?: boolean | {
    labelWidth?: number | 'auto'
    span?: number
    collapsed?: boolean
    collapseRender?: (collapsed: boolean) => ReactNode
    optionRender?: (searchConfig: any, formProps: any, dom: ReactNode[]) => ReactNode[]
  }
  
  // 工具栏
  toolBarRender?: (action: any, rows: { selectedRowKeys?: React.Key[], selectedRows?: T[] }) => ReactNode[]
  toolbar?: ToolBarConfig<T>
  
  // 分页
  pagination?: false | PaginationConfig
  
  // 选择
  rowSelection?: boolean | {
    type?: 'checkbox' | 'radio'
    selectedRowKeys?: React.Key[]
    onChange?: (selectedRowKeys: React.Key[], selectedRows: T[]) => void
    getCheckboxProps?: (record: T) => { disabled?: boolean, name?: string }
  }
  
  // 样式
  className?: string
  style?: React.CSSProperties
  
  // 事件
  onRow?: (record: T, index?: number) => React.HTMLAttributes<any>
  onHeaderRow?: (columns: ProColumnType<T>[], index?: number) => React.HTMLAttributes<any>
  
  // 其他
  scroll?: { x?: number | string, y?: number | string }
  expandable?: {
    expandedRowRender?: (record: T, index: number, indent: number, expanded: boolean) => ReactNode
    rowExpandable?: (record: T) => boolean
  }
  
  // 表格实例引用
  actionRef?: React.MutableRefObject<{
    reload: () => void
    reset: () => void
    clearSelected: () => void
    getDataSource: () => T[]
    getSelected: () => { keys: React.Key[], rows: T[] }
  } | undefined>
}

// 内部使用的表格操作接口
export interface ProTableActionType<T = any> {
  reload: () => void
  reset: () => void
  clearSelected: () => void
  getDataSource: () => T[]
  getSelected: () => { keys: React.Key[], rows: T[] }
  setLoading: (loading: boolean) => void
  setDataSource: (dataSource: T[]) => void
}
