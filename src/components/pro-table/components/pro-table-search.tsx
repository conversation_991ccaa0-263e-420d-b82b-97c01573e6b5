import { useState, useCallback, useMemo } from 'react'
import { Search, X, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import { ProColumnType } from '../types'
import { getSearchComponent } from '../utils/render-value-type'

interface ProTableSearchProps<T> {
  columns: ProColumnType<T>[]
  onSearch: (values: Record<string, unknown>) => void
  onReset: () => void
  collapsed?: boolean
  labelWidth?: number | 'auto'
}

export function ProTableSearch<T>({
  columns,
  onSearch,
  onReset,
  collapsed = false,
  labelWidth = 'auto',
}: ProTableSearchProps<T>) {
  const [searchValues, setSearchValues] = useState<Record<string, unknown>>({})
  const [isCollapsed, setIsCollapsed] = useState(collapsed)

  // 获取可搜索的列 - 使用 useMemo 优化性能
  const searchableColumns = useMemo(() =>
    columns.filter(col =>
      col.search !== false && !col.hideInSearch && col.dataIndex
    ), [columns]
  )

  // 使用 useCallback 优化回调函数
  const handleSearch = useCallback(() => {
    // 过滤空值
    const filteredValues = Object.entries(searchValues).reduce((acc, [key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, unknown>)

    onSearch(filteredValues)
  }, [searchValues, onSearch])

  const handleReset = useCallback(() => {
    setSearchValues({})
    onReset()
  }, [onReset])

  const handleValueChange = useCallback((key: string, value: unknown) => {
    setSearchValues(prev => ({
      ...prev,
      [key]: value,
    }))
  }, [])

  // 检查是否有搜索值
  const hasValues = useMemo(() =>
    Object.values(searchValues).some(value =>
      value !== '' && value !== null && value !== undefined
    ), [searchValues]
  )

  const renderSearchField = (column: ProColumnType<T>) => {
    const { dataIndex, title, valueType = 'text', valueEnum, search } = column
    const key = dataIndex as string
    const searchConfig = typeof search === 'object' ? search : {}
    const placeholder = searchConfig.placeholder || `请输入${title}`

    const searchComponent = getSearchComponent(valueType, { valueEnum })

    switch (searchComponent.type) {
      case 'select':
        return (
          <Select
            value={searchValues[key] ? String(searchValues[key]) : '__all__'}
            onValueChange={(value) => handleValueChange(key, value === '__all__' ? '' : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="__all__">全部</SelectItem>
              {searchComponent.options?.map((option) => (
                <SelectItem
                  key={option.value || 'empty'}
                  value={String(option.value || 'empty')}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'number':
        return (
          <Input
            type="number"
            placeholder={placeholder}
            value={searchValues[key] || ''}
            onChange={(e) => handleValueChange(key, e.target.value)}
          />
        )

      case 'date':
        return (
          <Input
            type="date"
            placeholder={placeholder}
            value={searchValues[key] || ''}
            onChange={(e) => handleValueChange(key, e.target.value)}
          />
        )

      default:
        return (
          <Input
            placeholder={placeholder}
            value={searchValues[key] || ''}
            onChange={(e) => handleValueChange(key, e.target.value)}
          />
        )
    }
  }

  // 如果没有可搜索的列，不渲染组件
  if (searchableColumns.length === 0) {
    return null
  }

  return (
    <div className="border rounded-lg p-4 bg-background/50 backdrop-blur-sm">
      <Collapsible open={!isCollapsed} onOpenChange={setIsCollapsed}>
        <div className="flex items-center justify-between">
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "p-0 h-auto hover:bg-transparent",
                "flex items-center gap-2 text-sm font-medium"
              )}
            >
              <Search className="h-4 w-4" />
              搜索条件
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>

          <div className="flex items-center space-x-2">
            <Button onClick={handleSearch} size="sm" className="h-8">
              <Search className="h-3 w-3 mr-1" />
              查询
            </Button>
            {hasValues && (
              <Button onClick={handleReset} variant="outline" size="sm" className="h-8">
                <X className="h-3 w-3 mr-1" />
                重置
              </Button>
            )}
          </div>
        </div>

        <CollapsibleContent className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {searchableColumns.map((column) => {
              const key = column.dataIndex as string
              return (
                <div key={key} className="space-y-2">
                  <Label
                    htmlFor={key}
                    className="text-sm font-medium text-muted-foreground"
                    style={{
                      width: labelWidth === 'auto' ? 'auto' : labelWidth,
                      minWidth: labelWidth === 'auto' ? 'auto' : labelWidth,
                    }}
                  >
                    {column.title}
                  </Label>
                  {renderSearchField(column)}
                </div>
              )
            })}
          </div>

          {/* 搜索统计信息 */}
          {hasValues && (
            <div className="mt-4 pt-4 border-t border-border/50">
              <div className="text-xs text-muted-foreground">
                已设置 {Object.keys(searchValues).filter(key =>
                  searchValues[key] !== '' &&
                  searchValues[key] !== null &&
                  searchValues[key] !== undefined
                ).length} 个搜索条件
              </div>
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
