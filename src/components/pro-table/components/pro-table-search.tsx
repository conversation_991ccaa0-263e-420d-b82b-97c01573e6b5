import { useState } from 'react'
import { Search, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { ProColumnType } from '../types'
import { getSearchComponent } from '../utils/render-value-type'

interface ProTableSearchProps<T> {
  columns: ProColumnType<T>[]
  onSearch: (values: Record<string, unknown>) => void
  onReset: () => void
  collapsed?: boolean
  labelWidth?: number | 'auto'
}

export function ProTableSearch<T>({
  columns,
  onSearch,
  onReset,
  collapsed = false,
  labelWidth = 'auto',
}: ProTableSearchProps<T>) {
  const [searchValues, setSearchValues] = useState<Record<string, unknown>>({})
  const [isCollapsed, setIsCollapsed] = useState(collapsed)

  // 获取可搜索的列
  const searchableColumns = columns.filter(col => 
    col.search !== false && !col.hideInSearch && col.dataIndex
  )

  const handleSearch = () => {
    onSearch(searchValues)
  }

  const handleReset = () => {
    setSearchValues({})
    onReset()
  }

  const handleValueChange = (key: string, value: unknown) => {
    setSearchValues(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  const renderSearchField = (column: ProColumnType<T>) => {
    const { dataIndex, title, valueType = 'text', valueEnum, search } = column
    const key = dataIndex as string
    const searchConfig = typeof search === 'object' ? search : {}
    const placeholder = searchConfig.placeholder || `请输入${title}`

    const searchComponent = getSearchComponent(valueType, { valueEnum })

    switch (searchComponent.type) {
      case 'select':
        return (
          <Select
            value={searchValues[key] || ''}
            onValueChange={(value) => handleValueChange(key, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部</SelectItem>
              {searchComponent.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'number':
        return (
          <Input
            type="number"
            placeholder={placeholder}
            value={searchValues[key] || ''}
            onChange={(e) => handleValueChange(key, e.target.value)}
          />
        )

      case 'date':
        return (
          <Input
            type="date"
            placeholder={placeholder}
            value={searchValues[key] || ''}
            onChange={(e) => handleValueChange(key, e.target.value)}
          />
        )

      default:
        return (
          <Input
            placeholder={placeholder}
            value={searchValues[key] || ''}
            onChange={(e) => handleValueChange(key, e.target.value)}
          />
        )
    }
  }

  if (searchableColumns.length === 0) {
    return null
  }

  const hasValues = Object.values(searchValues).some(value => value)

  return (
    <div className="border rounded-lg p-4 bg-background">
      <Collapsible open={!isCollapsed} onOpenChange={setIsCollapsed}>
        <div className="flex items-center justify-between">
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-auto">
              <Search className="h-4 w-4 mr-2" />
              搜索条件
            </Button>
          </CollapsibleTrigger>
          
          <div className="flex items-center space-x-2">
            <Button onClick={handleSearch} size="sm">
              查询
            </Button>
            {hasValues && (
              <Button onClick={handleReset} variant="outline" size="sm">
                <X className="h-4 w-4 mr-1" />
                重置
              </Button>
            )}
          </div>
        </div>

        <CollapsibleContent className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {searchableColumns.map((column) => {
              const key = column.dataIndex as string
              return (
                <div key={key} className="space-y-2">
                  <Label 
                    htmlFor={key}
                    className="text-sm font-medium"
                    style={{ 
                      width: labelWidth === 'auto' ? 'auto' : labelWidth,
                      minWidth: labelWidth === 'auto' ? 'auto' : labelWidth,
                    }}
                  >
                    {column.title}
                  </Label>
                  {renderSearchField(column)}
                </div>
              )
            })}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
