import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProTableLoadingProps {
  loading?: boolean
  rows?: number
  columns?: number
  className?: string
}

export function ProTableLoading({ 
  loading = true, 
  rows = 5, 
  columns = 4,
  className 
}: ProTableLoadingProps) {
  if (!loading) return null

  return (
    <div className={cn("space-y-4", className)}>
      {/* 工具栏骨架 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          <div className="h-8 w-24 bg-muted animate-pulse rounded" />
        </div>
        <div className="flex items-center space-x-2">
          <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          <div className="h-8 w-20 bg-muted animate-pulse rounded" />
        </div>
      </div>

      {/* 表格骨架 */}
      <div className="rounded-md border">
        <div className="border-b bg-muted/50">
          <div className="flex">
            {Array.from({ length: columns }).map((_, index) => (
              <div 
                key={index} 
                className="flex-1 p-4 border-r last:border-r-0"
              >
                <div className="h-4 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </div>
        
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="flex border-b last:border-b-0">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div 
                key={colIndex} 
                className="flex-1 p-4 border-r last:border-r-0"
              >
                <div 
                  className="h-4 bg-muted animate-pulse rounded"
                  style={{ 
                    width: `${60 + Math.random() * 40}%`,
                    animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s`
                  }}
                />
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* 分页骨架 */}
      <div className="flex items-center justify-between">
        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
        <div className="flex items-center space-x-2">
          <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
        </div>
      </div>
    </div>
  )
}

// 表格内容加载状态
export function ProTableContentLoading({ 
  columns = 4,
  className 
}: { 
  columns?: number
  className?: string 
}) {
  return (
    <div className={cn("flex items-center justify-center py-12", className)}>
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <div className="text-sm text-muted-foreground">加载中...</div>
      </div>
    </div>
  )
}

// 空状态类型
export type EmptyType = 'no-data' | 'search-empty' | 'filter-empty' | 'error' | 'loading-failed'

// 空状态组件
export function ProTableEmpty({
  type = 'no-data',
  description,
  action,
  className
}: {
  type?: EmptyType
  description?: string
  action?: React.ReactNode
  className?: string
}) {
  // 根据类型获取默认描述和图标
  const getEmptyConfig = (type: EmptyType) => {
    switch (type) {
      case 'search-empty':
        return {
          description: '搜索无结果',
          icon: (
            <svg className="h-8 w-8 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          )
        }
      case 'filter-empty':
        return {
          description: '筛选无结果',
          icon: (
            <svg className="h-8 w-8 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
          )
        }
      case 'error':
        return {
          description: '加载失败',
          icon: (
            <svg className="h-8 w-8 text-destructive" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          )
        }
      case 'loading-failed':
        return {
          description: '数据加载失败',
          icon: (
            <svg className="h-8 w-8 text-destructive" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
            </svg>
          )
        }
      default: // 'no-data'
        return {
          description: '暂无数据',
          icon: (
            <svg className="h-8 w-8 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          )
        }
    }
  }

  const config = getEmptyConfig(type)
  const finalDescription = description || config.description

  return (
    <div className={cn("flex items-center justify-center py-12", className)}>
      <div className="flex flex-col items-center space-y-4 max-w-sm text-center">
        <div className={cn(
          "h-16 w-16 rounded-full flex items-center justify-center",
          type === 'error' || type === 'loading-failed'
            ? "bg-destructive/10"
            : "bg-muted"
        )}>
          {config.icon}
        </div>
        <div className={cn(
          "text-sm",
          type === 'error' || type === 'loading-failed'
            ? "text-destructive"
            : "text-muted-foreground"
        )}>
          {finalDescription}
        </div>
        {action && <div className="mt-2">{action}</div>}
      </div>
    </div>
  )
}
