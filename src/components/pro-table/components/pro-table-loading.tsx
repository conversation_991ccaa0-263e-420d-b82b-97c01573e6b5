import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProTableLoadingProps {
  loading?: boolean
  rows?: number
  columns?: number
  className?: string
}

export function ProTableLoading({ 
  loading = true, 
  rows = 5, 
  columns = 4,
  className 
}: ProTableLoadingProps) {
  if (!loading) return null

  return (
    <div className={cn("space-y-4", className)}>
      {/* 工具栏骨架 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          <div className="h-8 w-24 bg-muted animate-pulse rounded" />
        </div>
        <div className="flex items-center space-x-2">
          <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          <div className="h-8 w-20 bg-muted animate-pulse rounded" />
        </div>
      </div>

      {/* 表格骨架 */}
      <div className="rounded-md border">
        <div className="border-b bg-muted/50">
          <div className="flex">
            {Array.from({ length: columns }).map((_, index) => (
              <div 
                key={index} 
                className="flex-1 p-4 border-r last:border-r-0"
              >
                <div className="h-4 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </div>
        
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="flex border-b last:border-b-0">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div 
                key={colIndex} 
                className="flex-1 p-4 border-r last:border-r-0"
              >
                <div 
                  className="h-4 bg-muted animate-pulse rounded"
                  style={{ 
                    width: `${60 + Math.random() * 40}%`,
                    animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s`
                  }}
                />
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* 分页骨架 */}
      <div className="flex items-center justify-between">
        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
        <div className="flex items-center space-x-2">
          <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
        </div>
      </div>
    </div>
  )
}

// 表格内容加载状态
export function ProTableContentLoading({ 
  columns = 4,
  className 
}: { 
  columns?: number
  className?: string 
}) {
  return (
    <div className={cn("flex items-center justify-center py-12", className)}>
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <div className="text-sm text-muted-foreground">加载中...</div>
      </div>
    </div>
  )
}

// 空状态组件
export function ProTableEmpty({ 
  description = "暂无数据",
  action,
  className 
}: { 
  description?: string
  action?: React.ReactNode
  className?: string 
}) {
  return (
    <div className={cn("flex items-center justify-center py-12", className)}>
      <div className="flex flex-col items-center space-y-4">
        <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center">
          <svg
            className="h-8 w-8 text-muted-foreground"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <div className="text-sm text-muted-foreground">{description}</div>
        {action && <div>{action}</div>}
      </div>
    </div>
  )
}
