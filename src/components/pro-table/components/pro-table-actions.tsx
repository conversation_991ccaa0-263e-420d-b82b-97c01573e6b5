import React from 'react'
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface ProTableActionsProps<T = any> {
  record: T
  actions?: Array<{
    key: string
    label: string
    icon?: React.ComponentType<any>
    onClick?: (record: T) => void
    disabled?: boolean | ((record: T) => boolean)
    danger?: boolean
  }>
}

export function ProTableActions<T>({ 
  record, 
  actions = [
    { key: 'edit', label: '编辑', onClick: () => console.log('编辑', record) },
    { key: 'delete', label: '删除', onClick: () => console.log('删除', record), danger: true },
  ]
}: ProTableActionsProps<T>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <DotsHorizontalIcon className="h-4 w-4" />
          <span className="sr-only">打开菜单</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {actions.map((action, index) => {
          const isDisabled = typeof action.disabled === 'function' 
            ? action.disabled(record) 
            : action.disabled

          return (
            <React.Fragment key={action.key}>
              <DropdownMenuItem
                onClick={() => !isDisabled && action.onClick?.(record)}
                disabled={isDisabled}
                className={action.danger ? 'text-red-600' : ''}
              >
                {action.icon && <action.icon className="mr-2 h-4 w-4" />}
                {action.label}
              </DropdownMenuItem>
              {index < actions.length - 1 && action.danger && (
                <DropdownMenuSeparator />
              )}
            </React.Fragment>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
