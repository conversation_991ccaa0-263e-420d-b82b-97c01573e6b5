import React from 'react'
import { Cross2Icon, ReloadIcon } from '@radix-ui/react-icons'
import { Search } from 'lucide-react'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ProColumnType, ToolBarConfig } from '../types'
import { ProTableViewOptions } from './pro-table-view-options'
import { ProTableFacetedFilter } from './pro-table-faceted-filter'

interface ProTableToolbarProps<T> {
  table: Table<T>
  columns: ProColumnType<T>[]
  search?: boolean | {
    placeholder?: string
    labelWidth?: number | 'auto'
    span?: number
    collapsed?: boolean
  }
  toolbar?: ToolBarConfig<T>
  loading?: boolean
  onReload?: () => void
  globalFilter?: string
  onGlobalFilterChange?: (value: string) => void
}

export function ProTableToolbar<T>({
  table,
  columns,
  search = true,
  toolbar = {},
  loading = false,
  onReload,
  globalFilter = '',
  onGlobalFilterChange,
}: ProTableToolbarProps<T>) {
  const isFiltered = table.getState().columnFilters.length > 0 || globalFilter

  // 获取可筛选的列
  const filterableColumns = columns.filter(col => 
    col.filters && !col.hideInTable && col.valueEnum
  )

  const searchConfig = typeof search === 'object' ? search : {}
  const searchPlaceholder = searchConfig.placeholder || '搜索...'

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2">
        {/* 全局搜索 */}
        {search && (
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={globalFilter}
              onChange={(event) => onGlobalFilterChange?.(event.target.value)}
              className="h-8 w-[150px] pl-8 lg:w-[250px]"
            />
          </div>
        )}

        {/* 列筛选器 */}
        <div className="flex gap-x-2">
          {filterableColumns.map((column) => {
            const tableColumn = table.getColumn(column.dataIndex as string)
            if (!tableColumn) return null

            return (
              <ProTableFacetedFilter
                key={column.key || column.dataIndex as string}
                column={tableColumn}
                title={column.title as string}
                options={Object.entries(column.valueEnum || {}).map(([value, option]) => ({
                  value,
                  label: typeof option === 'string' ? option : option.label,
                  icon: typeof option === 'object' ? option.icon : undefined,
                }))}
              />
            )
          })}

          {/* 清除筛选 */}
          {isFiltered && (
            <Button
              variant="ghost"
              onClick={() => {
                table.resetColumnFilters()
                onGlobalFilterChange?.('')
              }}
              className="h-8 px-2 lg:px-3"
            >
              重置
              <Cross2Icon className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 右侧工具栏 */}
      <div className="flex items-center space-x-2">
        {/* 刷新按钮 */}
        {(toolbar.refresh !== false) && (
          <Button
            variant="outline"
            size="sm"
            onClick={onReload}
            disabled={loading}
            className="h-8"
          >
            <ReloadIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        )}

        {/* 自定义操作 */}
        {toolbar.actions?.map((action, index) => (
          <React.Fragment key={index}>{action}</React.Fragment>
        ))}

        {/* 列设置 */}
        {toolbar.setting !== false && (
          <ProTableViewOptions table={table} />
        )}
      </div>
    </div>
  )
}
