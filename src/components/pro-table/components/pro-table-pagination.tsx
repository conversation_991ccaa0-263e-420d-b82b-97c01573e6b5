
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
  DotsHorizontalIcon,
} from '@radix-ui/react-icons'
import { Settings2 } from 'lucide-react'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PaginationConfig } from '../types'

interface ProTablePaginationProps<TData> {
  table: Table<TData>
  total?: number
  current?: number
  pageSize?: number
  pagination?: PaginationConfig
  loading?: boolean
  onPageChange?: (page: number, pageSize: number) => void
}

export function ProTablePagination<TData>({
  table,
  total = 0,
  current = 1,
  pageSize = 10,
  pagination = {},
  loading = false,
  onPageChange,
}: ProTablePaginationProps<TData>) {
  const { t } = useTranslation()
  const [showSettings, setShowSettings] = useState(false)
  const [jumpToPage, setJumpToPage] = useState('')

  const {
    showSizeChanger = true,
    showQuickJumper = false,
    showTotal = true,
    pageSizeOptions = ['10', '20', '30', '40', '50'],
  } = pagination

  const totalPages = Math.ceil(total / pageSize)
  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length
  const startItem = (current - 1) * pageSize + 1
  const endItem = Math.min(current * pageSize, total)

  const handlePageSizeChange = (newPageSize: string) => {
    const size = Number(newPageSize)
    onPageChange?.(1, size) // 改变页面大小时回到第一页
  }

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange?.(newPage, pageSize)
    }
  }

  const handleJumpToPage = () => {
    const page = Number(jumpToPage)
    if (page >= 1 && page <= totalPages) {
      handlePageChange(page)
      setJumpToPage('')
      setShowSettings(false)
    }
  }

  return (
    <div className="flex flex-col gap-4 px-2 py-4 md:flex-row md:items-center md:justify-between md:gap-0">
      {/* 桌面端：左侧统计信息 */}
      <div className="hidden text-sm text-muted-foreground md:block">
        {showTotal && typeof showTotal === 'function' ? (
          showTotal(total, [startItem, endItem])
        ) : showTotal ? (
          selectedRowsCount > 0 ? (
            t('proTable.pagination.showTotal', {
              start: startItem,
              end: endItem,
              total,
              selected: selectedRowsCount
            })
          ) : (
            t('proTable.pagination.showTotal', { start: startItem, end: endItem, total })
          )
        ) : null}
      </div>

      {/* 移动端：简化的统计信息 */}
      <div className="flex items-center justify-center text-sm text-muted-foreground md:hidden">
        {t('proTable.pagination.current', { current })} / {t('proTable.pagination.total', { total: totalPages })}
      </div>

      {/* 分页控件容器 */}
      <div className="flex items-center justify-between gap-2 md:justify-end md:gap-6">
        {/* 移动端：设置按钮（包含页面大小选择和跳转） */}
        <div className="md:hidden">
          <Popover open={showSettings} onOpenChange={setShowSettings}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-9 w-9 p-0">
                <Settings2 className="h-4 w-4" />
                <span className="sr-only">{t('proTable.toolbar.settings')}</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    {t('proTable.pagination.itemsPerPage')}
                  </Label>
                  <Select
                    value={`${pageSize}`}
                    onValueChange={handlePageSizeChange}
                    disabled={loading}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {pageSizeOptions.map((size) => (
                        <SelectItem key={size} value={size}>
                          {size} {t('proTable.pagination.itemsPerPage')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {showQuickJumper && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      {t('proTable.pagination.goto')}
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        placeholder="1"
                        value={jumpToPage}
                        onChange={(e) => setJumpToPage(e.target.value)}
                        className="h-9"
                        min={1}
                        max={totalPages}
                      />
                      <Button
                        onClick={handleJumpToPage}
                        size="sm"
                        className="h-9"
                        disabled={!jumpToPage || loading}
                      >
                        {t('proTable.pagination.goto')}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* 桌面端：页面大小选择器 */}
        {showSizeChanger && (
          <div className="hidden items-center gap-2 md:flex">
            <span className="text-sm font-medium">
              {t('proTable.pagination.itemsPerPage')}
            </span>
            <Select
              value={`${pageSize}`}
              onValueChange={handlePageSizeChange}
              disabled={loading}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent side="top">
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* 桌面端：页码信息 */}
        <div className="hidden items-center justify-center text-sm font-medium md:flex md:w-[120px]">
          {t('proTable.pagination.current', { current })} / {totalPages}
        </div>

        {/* 分页按钮组 */}
        <div className="flex items-center gap-1">
          {/* 第一页按钮 - 仅桌面端显示 */}
          <Button
            variant="outline"
            className="hidden h-9 w-9 p-0 lg:flex"
            onClick={() => handlePageChange(1)}
            disabled={current <= 1 || loading}
          >
            <span className="sr-only">{t('proTable.pagination.first')}</span>
            <DoubleArrowLeftIcon className="h-4 w-4" />
          </Button>

          {/* 上一页按钮 - 移动端增大触摸区域 */}
          <Button
            variant="outline"
            className="h-9 w-9 p-0 md:h-8 md:w-8"
            onClick={() => handlePageChange(current - 1)}
            disabled={current <= 1 || loading}
          >
            <span className="sr-only">{t('proTable.pagination.prev')}</span>
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>

          {/* 移动端：当前页码显示 */}
          <div className="flex h-9 min-w-[60px] items-center justify-center rounded-md border bg-background px-3 text-sm font-medium md:hidden">
            {current}
          </div>

          {/* 桌面端：页码范围显示 */}
          <div className="hidden md:flex md:items-center md:gap-1">
            {/* 显示当前页前后的页码 */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum: number
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (current <= 3) {
                pageNum = i + 1
              } else if (current >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = current - 2 + i
              }

              if (pageNum < 1 || pageNum > totalPages) return null

              return (
                <Button
                  key={pageNum}
                  variant={pageNum === current ? "default" : "outline"}
                  className="h-8 w-8 p-0"
                  onClick={() => handlePageChange(pageNum)}
                  disabled={loading}
                >
                  {pageNum}
                </Button>
              )
            })}

            {/* 省略号和最后一页 */}
            {totalPages > 5 && current < totalPages - 2 && (
              <>
                <DotsHorizontalIcon className="h-4 w-4 text-muted-foreground" />
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={loading}
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>

          {/* 下一页按钮 - 移动端增大触摸区域 */}
          <Button
            variant="outline"
            className="h-9 w-9 p-0 md:h-8 md:w-8"
            onClick={() => handlePageChange(current + 1)}
            disabled={current >= totalPages || loading}
          >
            <span className="sr-only">{t('proTable.pagination.next')}</span>
            <ChevronRightIcon className="h-4 w-4" />
          </Button>

          {/* 最后一页按钮 - 仅桌面端显示 */}
          <Button
            variant="outline"
            className="hidden h-9 w-9 p-0 lg:flex"
            onClick={() => handlePageChange(totalPages)}
            disabled={current >= totalPages || loading}
          >
            <span className="sr-only">{t('proTable.pagination.last')}</span>
            <DoubleArrowRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
