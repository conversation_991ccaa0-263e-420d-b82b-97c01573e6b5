
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { PaginationConfig } from '../types'

interface ProTablePaginationProps<TData> {
  table: Table<TData>
  total?: number
  current?: number
  pageSize?: number
  pagination?: PaginationConfig
  loading?: boolean
  onPageChange?: (page: number, pageSize: number) => void
}

export function ProTablePagination<TData>({
  table,
  total = 0,
  current = 1,
  pageSize = 10,
  pagination = {},
  loading = false,
  onPageChange,
}: ProTablePaginationProps<TData>) {
  const {
    showSizeChanger = true,
    showQuickJumper: _showQuickJumper = false,
    showTotal = true,
    pageSizeOptions = ['10', '20', '30', '40', '50'],
  } = pagination

  const totalPages = Math.ceil(total / pageSize)
  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length
  const totalRowsCount = table.getFilteredRowModel().rows.length

  const handlePageSizeChange = (newPageSize: string) => {
    const size = Number(newPageSize)
    onPageChange?.(1, size) // 改变页面大小时回到第一页
  }

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange?.(newPage, pageSize)
    }
  }

  return (
    <div
      className="flex items-center justify-between overflow-clip px-2"
      style={{ overflowClipMargin: 1 }}
    >
      {/* 左侧：选中信息 */}
      <div className="text-muted-foreground hidden flex-1 text-sm sm:block">
        {showTotal && typeof showTotal === 'function' ? (
          showTotal(total, [(current - 1) * pageSize + 1, Math.min(current * pageSize, total)])
        ) : showTotal ? (
          <>
            已选择 {selectedRowsCount} 项，共 {total} 项
          </>
        ) : (
          `${selectedRowsCount} / ${totalRowsCount} 行已选择`
        )}
      </div>

      {/* 右侧：分页控件 */}
      <div className="flex items-center sm:space-x-6 lg:space-x-8">
        {/* 每页显示数量 */}
        {showSizeChanger && (
          <div className="flex items-center space-x-2">
            <p className="hidden text-sm font-medium sm:block">每页显示</p>
            <Select
              value={`${pageSize}`}
              onValueChange={handlePageSizeChange}
              disabled={loading}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* 页码信息 */}
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          第 {current} 页，共 {totalPages} 页
        </div>

        {/* 分页按钮 */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => handlePageChange(1)}
            disabled={current <= 1 || loading}
          >
            <span className="sr-only">跳转到第一页</span>
            <DoubleArrowLeftIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => handlePageChange(current - 1)}
            disabled={current <= 1 || loading}
          >
            <span className="sr-only">上一页</span>
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => handlePageChange(current + 1)}
            disabled={current >= totalPages || loading}
          >
            <span className="sr-only">下一页</span>
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => handlePageChange(totalPages)}
            disabled={current >= totalPages || loading}
          >
            <span className="sr-only">跳转到最后一页</span>
            <DoubleArrowRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
