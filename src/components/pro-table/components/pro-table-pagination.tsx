
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
  DotsHorizontalIcon,
} from '@radix-ui/react-icons'
import { Settings2 } from 'lucide-react'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PaginationConfig } from '../types'

interface ProTablePaginationProps<TData> {
  table: Table<TData>
  total?: number
  current?: number
  pageSize?: number
  pagination?: PaginationConfig
  loading?: boolean
  onPageChange?: (page: number, pageSize: number) => void
}

export function ProTablePagination<TData>({
  table,
  total = 0,
  current = 1,
  pageSize = 10,
  pagination = {},
  loading = false,
  onPageChange,
}: ProTablePaginationProps<TData>) {
  const { t } = useTranslation()
  const [showSettings, setShowSettings] = useState(false)
  const [jumpToPage, setJumpToPage] = useState('')

  const {
    showSizeChanger = true,
    showQuickJumper = false,
    showTotal = true,
    pageSizeOptions = ['10', '20', '30', '40', '50'],
  } = pagination

  const totalPages = Math.ceil(total / pageSize)
  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length
  const startItem = (current - 1) * pageSize + 1
  const endItem = Math.min(current * pageSize, total)

  const handlePageSizeChange = (newPageSize: string) => {
    const size = Number(newPageSize)
    onPageChange?.(1, size) // 改变页面大小时回到第一页
  }

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange?.(newPage, pageSize)
    }
  }

  const handleJumpToPage = () => {
    const page = Number(jumpToPage)
    if (page >= 1 && page <= totalPages) {
      handlePageChange(page)
      setJumpToPage('')
      setShowSettings(false)
    }
  }

  return (
    <div className="pro-table-pagination flex items-center justify-between px-2 py-4">
      {/* 左侧：统计信息 */}
      <div className="flex-1 text-sm text-muted-foreground">
        {showTotal && typeof showTotal === 'function' ? (
          showTotal(total, [startItem, endItem])
        ) : showTotal ? (
          <div className="hidden sm:block">
            {selectedRowsCount > 0 ? (
              <>
                {t('proTable.pagination.showTotal', { start: startItem, end: endItem, total })}
                <span className="ml-2 text-primary">
                  ({t('proTable.selection.selectedItems', { count: selectedRowsCount })})
                </span>
              </>
            ) : (
              t('proTable.pagination.showTotal', { start: startItem, end: endItem, total })
            )}
          </div>
        ) : null}

        {/* 移动端简化显示 */}
        <div className="block sm:hidden">
          {selectedRowsCount > 0 ? (
            <span className="text-primary">
              已选择 {selectedRowsCount} 项
            </span>
          ) : (
            <span>
              第 {current} 页 / 共 {totalPages} 页
            </span>
          )}
        </div>
      </div>

      {/* 右侧：分页控件 */}
      <div className="flex items-center gap-2 sm:gap-6">
        {/* 桌面端：页面大小选择器 */}
        {showSizeChanger && (
          <div className="hidden items-center gap-2 sm:flex">
            <span className="text-sm font-medium">
              {t('proTable.pagination.itemsPerPage')}
            </span>
            <Select
              value={`${pageSize}`}
              onValueChange={handlePageSizeChange}
              disabled={loading}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent side="top">
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* 桌面端：页码信息 */}
        <div className="hidden items-center justify-center text-sm font-medium sm:flex sm:w-[100px]">
          {current} / {totalPages}
        </div>

        {/* 分页按钮组 */}
        <div className="flex items-center gap-1">
          {/* 第一页按钮 - 仅大屏幕显示 */}
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => handlePageChange(1)}
            disabled={current <= 1 || loading}
          >
            <span className="sr-only">第一页</span>
            <DoubleArrowLeftIcon className="h-4 w-4" />
          </Button>

          {/* 上一页按钮 */}
          <Button
            variant="outline"
            className="h-7 w-7 p-0 sm:h-8 sm:w-8"
            onClick={() => handlePageChange(current - 1)}
            disabled={current <= 1 || loading}
          >
            <span className="sr-only">上一页</span>
            <ChevronLeftIcon className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>

          {/* 页码显示 */}
          <div className="flex items-center gap-1">
            {/* 移动端：简化的页码显示 */}
            <div className="flex h-7 min-w-[50px] items-center justify-center rounded-md border bg-background px-2 text-sm font-medium sm:hidden sm:h-8 sm:min-w-[60px] sm:px-3">
              {current}
            </div>

            {/* 桌面端：页码范围显示 */}
            <div className="hidden items-center gap-1 sm:flex">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum: number
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (current <= 3) {
                  pageNum = i + 1
                } else if (current >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = current - 2 + i
                }

                if (pageNum < 1 || pageNum > totalPages) return null

                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === current ? "default" : "outline"}
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(pageNum)}
                    disabled={loading}
                  >
                    {pageNum}
                  </Button>
                )
              })}

              {/* 省略号和最后一页 */}
              {totalPages > 5 && current < totalPages - 2 && (
                <>
                  <DotsHorizontalIcon className="h-4 w-4 text-muted-foreground" />
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={loading}
                  >
                    {totalPages}
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* 下一页按钮 */}
          <Button
            variant="outline"
            className="h-7 w-7 p-0 sm:h-8 sm:w-8"
            onClick={() => handlePageChange(current + 1)}
            disabled={current >= totalPages || loading}
          >
            <span className="sr-only">下一页</span>
            <ChevronRightIcon className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>

          {/* 最后一页按钮 - 仅大屏幕显示 */}
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => handlePageChange(totalPages)}
            disabled={current >= totalPages || loading}
          >
            <span className="sr-only">最后一页</span>
            <DoubleArrowRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
