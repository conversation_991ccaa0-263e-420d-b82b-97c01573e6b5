# ProTable 迁移指南

本指南帮助你从现有的 DataTable 组件迁移到新的 ProTable 组件。

## 迁移概述

ProTable 是基于 @tanstack/react-table 和 shadcn/ui 的高级表格组件，旨在简化表格的使用和配置。

### 主要优势

- **减少代码量**：从多个组件简化为单个组件
- **配置式 API**：通过简单配置实现复杂功能
- **内置功能**：搜索、筛选、分页、工具栏等功能内置
- **类型安全**：完整的 TypeScript 支持

## 迁移步骤

### 1. 更新导入

**之前：**
```tsx
import { DataTable } from './components/data-table'
import { DataTableToolbar } from './components/data-table-toolbar'
import { DataTablePagination } from './components/data-table-pagination'
import { columns } from './components/columns'
```

**现在：**
```tsx
import ProTable, { ProColumnType } from '@/components/pro-table'
```

### 2. 转换列定义

**之前的 ColumnDef：**
```tsx
export const columns: ColumnDef<Task>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = statuses.find(s => s.value === row.getValue('status'))
      return status ? <Badge>{status.label}</Badge> : null
    },
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
]
```

**现在的 ProColumnType：**
```tsx
const columns: ProColumnType<Task>[] = [
  {
    title: '状态',
    dataIndex: 'status',
    valueType: 'select',
    valueEnum: {
      active: '激活',
      inactive: '未激活',
      pending: '待审核',
    },
    filters: true,
    sorter: true,
  },
]
```

### 3. 更新组件使用

**之前：**
```tsx
function TasksPage() {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} />
      <div className="rounded-md border">
        <DataTable data={data} columns={columns} />
      </div>
      <DataTablePagination table={table} />
    </div>
  )
}
```

**现在：**
```tsx
function TasksPage() {
  return (
    <ProTable<Task>
      columns={columns}
      dataSource={data}
      rowKey="id"
      search={true}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
      }}
      rowSelection={{
        type: 'checkbox',
        onChange: (keys, rows) => {
          console.log('选中的行:', keys, rows)
        },
      }}
      toolbar={{
        refresh: true,
        setting: true,
      }}
    />
  )
}
```

## 功能映射

### 行选择

**之前：**
```tsx
// 需要手动管理状态
const [rowSelection, setRowSelection] = useState({})

// 在 table 配置中启用
enableRowSelection: true,
onRowSelectionChange: setRowSelection,
```

**现在：**
```tsx
// 简单配置即可
rowSelection={{
  type: 'checkbox',
  onChange: (keys, rows) => {
    // 处理选择变化
  },
}}
```

### 搜索和筛选

**之前：**
```tsx
// 需要自定义工具栏组件
<DataTableToolbar table={table} />

// 在列定义中添加筛选
filterFn: (row, id, value) => value.includes(row.getValue(id)),
```

**现在：**
```tsx
// 列配置中启用
{
  title: '状态',
  dataIndex: 'status',
  valueType: 'select',
  valueEnum: { ... },
  filters: true,
  search: {
    placeholder: '搜索状态...',
  },
}
```

### 分页

**之前：**
```tsx
// 需要单独的分页组件
<DataTablePagination table={table} />

// 在 table 配置中启用
getPaginationRowModel: getPaginationRowModel(),
```

**现在：**
```tsx
// 简单配置
pagination={{
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
}}
```

## 常见迁移问题

### 1. 类型约束

**问题：** `Type 'MyData' does not satisfy the constraint 'Record<string, unknown>'`

**解决：**
```tsx
// 为数据类型添加约束
interface MyData extends Record<string, unknown> {
  id: string
  name: string
  // ... 其他字段
}
```

### 2. 自定义渲染

**问题：** 复杂的单元格渲染

**解决：**
```tsx
{
  title: '操作',
  key: 'actions',
  render: (_, record) => (
    <div className="flex space-x-2">
      <Button onClick={() => handleEdit(record)}>编辑</Button>
      <Button onClick={() => handleDelete(record)}>删除</Button>
    </div>
  ),
}
```

### 3. 远程数据

**问题：** 从本地数据切换到远程数据

**解决：**
```tsx
// 定义请求函数
const request = async (params) => {
  const response = await api.get('/users', { params })
  return {
    data: response.data.list,
    total: response.data.total,
    success: true,
  }
}

// 使用 request 替代 dataSource
<ProTable
  request={request}
  // 移除 dataSource
/>
```

## 迁移检查清单

- [ ] 更新导入语句
- [ ] 转换列定义为 ProColumnType
- [ ] 简化组件使用
- [ ] 添加类型约束
- [ ] 测试所有功能
- [ ] 更新相关文档
- [ ] 移除旧的组件文件

## 获取帮助

如果在迁移过程中遇到问题：

1. 查看 [README.md](./README.md) 了解详细 API
2. 访问 `/dashboard/pro-table-demo` 查看完整示例
3. 检查控制台错误信息
4. 验证数据类型和结构

迁移完成后，你将获得更简洁、更强大的表格组件！
