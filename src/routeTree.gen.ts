/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DashboardRouteImport } from './routes/dashboard/route'
import { Route as ClerkRouteImport } from './routes/clerk/route'
import { Route as IndexImport } from './routes/index'
import { Route as DashboardIndexImport } from './routes/dashboard/index'
import { Route as DashboardProTableSimpleImport } from './routes/dashboard/pro-table-simple'
import { Route as DashboardProTableDemoImport } from './routes/dashboard/pro-table-demo'
import { Route as DashboardOverviewImport } from './routes/dashboard/overview'
import { Route as marketingTermsImport } from './routes/(marketing)/terms'
import { Route as marketingPrivacyImport } from './routes/(marketing)/privacy'
import { Route as marketingPricingImport } from './routes/(marketing)/pricing'
import { Route as marketingFeaturesImport } from './routes/(marketing)/features'
import { Route as marketingContactImport } from './routes/(marketing)/contact'
import { Route as marketingApiTestImport } from './routes/(marketing)/api-test'
import { Route as marketingAboutImport } from './routes/(marketing)/about'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authSignUpImport } from './routes/(auth)/sign-up'
import { Route as authSignIn2Import } from './routes/(auth)/sign-in-2'
import { Route as authSignInImport } from './routes/(auth)/sign-in'
import { Route as authOtpImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordImport } from './routes/(auth)/forgot-password'
import { Route as DashboardSettingsRouteImport } from './routes/dashboard/settings/route'
import { Route as ClerkAuthenticatedRouteImport } from './routes/clerk/_authenticated/route'
import { Route as ClerkauthRouteImport } from './routes/clerk/(auth)/route'
import { Route as DashboardUsersIndexImport } from './routes/dashboard/users/index'
import { Route as DashboardTasksIndexImport } from './routes/dashboard/tasks/index'
import { Route as DashboardSettingsIndexImport } from './routes/dashboard/settings/index'
import { Route as DashboardHelpCenterIndexImport } from './routes/dashboard/help-center/index'
import { Route as DashboardChatsIndexImport } from './routes/dashboard/chats/index'
import { Route as DashboardAppsIndexImport } from './routes/dashboard/apps/index'
import { Route as marketingBlogIndexImport } from './routes/(marketing)/blog/index'
import { Route as DashboardSettingsNotificationsImport } from './routes/dashboard/settings/notifications'
import { Route as DashboardSettingsDisplayImport } from './routes/dashboard/settings/display'
import { Route as DashboardSettingsAppearanceImport } from './routes/dashboard/settings/appearance'
import { Route as DashboardSettingsAccountImport } from './routes/dashboard/settings/account'
import { Route as ClerkAuthenticatedUserManagementImport } from './routes/clerk/_authenticated/user-management'
import { Route as ClerkauthSignUpImport } from './routes/clerk/(auth)/sign-up'
import { Route as ClerkauthSignInImport } from './routes/clerk/(auth)/sign-in'
import { Route as marketingBlogSlugImport } from './routes/(marketing)/blog/$slug'

// Create/Update Routes

const DashboardRouteRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const ClerkRouteRoute = ClerkRouteImport.update({
  id: '/clerk',
  path: '/clerk',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardIndexRoute = DashboardIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardProTableSimpleRoute = DashboardProTableSimpleImport.update({
  id: '/pro-table-simple',
  path: '/pro-table-simple',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardProTableDemoRoute = DashboardProTableDemoImport.update({
  id: '/pro-table-demo',
  path: '/pro-table-demo',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardOverviewRoute = DashboardOverviewImport.update({
  id: '/overview',
  path: '/overview',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const marketingTermsRoute = marketingTermsImport.update({
  id: '/(marketing)/terms',
  path: '/terms',
  getParentRoute: () => rootRoute,
} as any)

const marketingPrivacyRoute = marketingPrivacyImport.update({
  id: '/(marketing)/privacy',
  path: '/privacy',
  getParentRoute: () => rootRoute,
} as any)

const marketingPricingRoute = marketingPricingImport.update({
  id: '/(marketing)/pricing',
  path: '/pricing',
  getParentRoute: () => rootRoute,
} as any)

const marketingFeaturesRoute = marketingFeaturesImport.update({
  id: '/(marketing)/features',
  path: '/features',
  getParentRoute: () => rootRoute,
} as any)

const marketingContactRoute = marketingContactImport.update({
  id: '/(marketing)/contact',
  path: '/contact',
  getParentRoute: () => rootRoute,
} as any)

const marketingApiTestRoute = marketingApiTestImport.update({
  id: '/(marketing)/api-test',
  path: '/api-test',
  getParentRoute: () => rootRoute,
} as any)

const marketingAboutRoute = marketingAboutImport.update({
  id: '/(marketing)/about',
  path: '/about',
  getParentRoute: () => rootRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authSignUpRoute = authSignUpImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const authSignIn2Route = authSignIn2Import.update({
  id: '/(auth)/sign-in-2',
  path: '/sign-in-2',
  getParentRoute: () => rootRoute,
} as any)

const authSignInRoute = authSignInImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const authOtpRoute = authOtpImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRoute,
} as any)

const authForgotPasswordRoute = authForgotPasswordImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const DashboardSettingsRouteRoute = DashboardSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const ClerkAuthenticatedRouteRoute = ClerkAuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const ClerkauthRouteRoute = ClerkauthRouteImport.update({
  id: '/(auth)',
  getParentRoute: () => ClerkRouteRoute,
} as any)

const DashboardUsersIndexRoute = DashboardUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardTasksIndexRoute = DashboardTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardSettingsIndexRoute = DashboardSettingsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardSettingsRouteRoute,
} as any)

const DashboardHelpCenterIndexRoute = DashboardHelpCenterIndexImport.update({
  id: '/help-center/',
  path: '/help-center/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardChatsIndexRoute = DashboardChatsIndexImport.update({
  id: '/chats/',
  path: '/chats/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardAppsIndexRoute = DashboardAppsIndexImport.update({
  id: '/apps/',
  path: '/apps/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const marketingBlogIndexRoute = marketingBlogIndexImport.update({
  id: '/(marketing)/blog/',
  path: '/blog/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardSettingsNotificationsRoute =
  DashboardSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => DashboardSettingsRouteRoute,
  } as any)

const DashboardSettingsDisplayRoute = DashboardSettingsDisplayImport.update({
  id: '/display',
  path: '/display',
  getParentRoute: () => DashboardSettingsRouteRoute,
} as any)

const DashboardSettingsAppearanceRoute =
  DashboardSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => DashboardSettingsRouteRoute,
  } as any)

const DashboardSettingsAccountRoute = DashboardSettingsAccountImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => DashboardSettingsRouteRoute,
} as any)

const ClerkAuthenticatedUserManagementRoute =
  ClerkAuthenticatedUserManagementImport.update({
    id: '/user-management',
    path: '/user-management',
    getParentRoute: () => ClerkAuthenticatedRouteRoute,
  } as any)

const ClerkauthSignUpRoute = ClerkauthSignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const ClerkauthSignInRoute = ClerkauthSignInImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => ClerkauthRouteRoute,
} as any)

const marketingBlogSlugRoute = marketingBlogSlugImport.update({
  id: '/(marketing)/blog/$slug',
  path: '/blog/$slug',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/clerk': {
      id: '/clerk'
      path: '/clerk'
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkRouteImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRoute
    }
    '/clerk/(auth)': {
      id: '/clerk/(auth)'
      path: '/'
      fullPath: '/clerk/'
      preLoaderRoute: typeof ClerkauthRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/clerk/_authenticated': {
      id: '/clerk/_authenticated'
      path: ''
      fullPath: '/clerk'
      preLoaderRoute: typeof ClerkAuthenticatedRouteImport
      parentRoute: typeof ClerkRouteImport
    }
    '/dashboard/settings': {
      id: '/dashboard/settings'
      path: '/settings'
      fullPath: '/dashboard/settings'
      preLoaderRoute: typeof DashboardSettingsRouteImport
      parentRoute: typeof DashboardRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in-2': {
      id: '/(auth)/sign-in-2'
      path: '/sign-in-2'
      fullPath: '/sign-in-2'
      preLoaderRoute: typeof authSignIn2Import
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/(marketing)/about': {
      id: '/(marketing)/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof marketingAboutImport
      parentRoute: typeof rootRoute
    }
    '/(marketing)/api-test': {
      id: '/(marketing)/api-test'
      path: '/api-test'
      fullPath: '/api-test'
      preLoaderRoute: typeof marketingApiTestImport
      parentRoute: typeof rootRoute
    }
    '/(marketing)/contact': {
      id: '/(marketing)/contact'
      path: '/contact'
      fullPath: '/contact'
      preLoaderRoute: typeof marketingContactImport
      parentRoute: typeof rootRoute
    }
    '/(marketing)/features': {
      id: '/(marketing)/features'
      path: '/features'
      fullPath: '/features'
      preLoaderRoute: typeof marketingFeaturesImport
      parentRoute: typeof rootRoute
    }
    '/(marketing)/pricing': {
      id: '/(marketing)/pricing'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof marketingPricingImport
      parentRoute: typeof rootRoute
    }
    '/(marketing)/privacy': {
      id: '/(marketing)/privacy'
      path: '/privacy'
      fullPath: '/privacy'
      preLoaderRoute: typeof marketingPrivacyImport
      parentRoute: typeof rootRoute
    }
    '/(marketing)/terms': {
      id: '/(marketing)/terms'
      path: '/terms'
      fullPath: '/terms'
      preLoaderRoute: typeof marketingTermsImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/overview': {
      id: '/dashboard/overview'
      path: '/overview'
      fullPath: '/dashboard/overview'
      preLoaderRoute: typeof DashboardOverviewImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/pro-table-demo': {
      id: '/dashboard/pro-table-demo'
      path: '/pro-table-demo'
      fullPath: '/dashboard/pro-table-demo'
      preLoaderRoute: typeof DashboardProTableDemoImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/pro-table-simple': {
      id: '/dashboard/pro-table-simple'
      path: '/pro-table-simple'
      fullPath: '/dashboard/pro-table-simple'
      preLoaderRoute: typeof DashboardProTableSimpleImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexImport
      parentRoute: typeof DashboardRouteImport
    }
    '/(marketing)/blog/$slug': {
      id: '/(marketing)/blog/$slug'
      path: '/blog/$slug'
      fullPath: '/blog/$slug'
      preLoaderRoute: typeof marketingBlogSlugImport
      parentRoute: typeof rootRoute
    }
    '/clerk/(auth)/sign-in': {
      id: '/clerk/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/clerk/sign-in'
      preLoaderRoute: typeof ClerkauthSignInImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/(auth)/sign-up': {
      id: '/clerk/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/clerk/sign-up'
      preLoaderRoute: typeof ClerkauthSignUpImport
      parentRoute: typeof ClerkauthRouteImport
    }
    '/clerk/_authenticated/user-management': {
      id: '/clerk/_authenticated/user-management'
      path: '/user-management'
      fullPath: '/clerk/user-management'
      preLoaderRoute: typeof ClerkAuthenticatedUserManagementImport
      parentRoute: typeof ClerkAuthenticatedRouteImport
    }
    '/dashboard/settings/account': {
      id: '/dashboard/settings/account'
      path: '/account'
      fullPath: '/dashboard/settings/account'
      preLoaderRoute: typeof DashboardSettingsAccountImport
      parentRoute: typeof DashboardSettingsRouteImport
    }
    '/dashboard/settings/appearance': {
      id: '/dashboard/settings/appearance'
      path: '/appearance'
      fullPath: '/dashboard/settings/appearance'
      preLoaderRoute: typeof DashboardSettingsAppearanceImport
      parentRoute: typeof DashboardSettingsRouteImport
    }
    '/dashboard/settings/display': {
      id: '/dashboard/settings/display'
      path: '/display'
      fullPath: '/dashboard/settings/display'
      preLoaderRoute: typeof DashboardSettingsDisplayImport
      parentRoute: typeof DashboardSettingsRouteImport
    }
    '/dashboard/settings/notifications': {
      id: '/dashboard/settings/notifications'
      path: '/notifications'
      fullPath: '/dashboard/settings/notifications'
      preLoaderRoute: typeof DashboardSettingsNotificationsImport
      parentRoute: typeof DashboardSettingsRouteImport
    }
    '/(marketing)/blog/': {
      id: '/(marketing)/blog/'
      path: '/blog'
      fullPath: '/blog'
      preLoaderRoute: typeof marketingBlogIndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/apps/': {
      id: '/dashboard/apps/'
      path: '/apps'
      fullPath: '/dashboard/apps'
      preLoaderRoute: typeof DashboardAppsIndexImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/chats/': {
      id: '/dashboard/chats/'
      path: '/chats'
      fullPath: '/dashboard/chats'
      preLoaderRoute: typeof DashboardChatsIndexImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/help-center/': {
      id: '/dashboard/help-center/'
      path: '/help-center'
      fullPath: '/dashboard/help-center'
      preLoaderRoute: typeof DashboardHelpCenterIndexImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/settings/': {
      id: '/dashboard/settings/'
      path: '/'
      fullPath: '/dashboard/settings/'
      preLoaderRoute: typeof DashboardSettingsIndexImport
      parentRoute: typeof DashboardSettingsRouteImport
    }
    '/dashboard/tasks/': {
      id: '/dashboard/tasks/'
      path: '/tasks'
      fullPath: '/dashboard/tasks'
      preLoaderRoute: typeof DashboardTasksIndexImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/users/': {
      id: '/dashboard/users/'
      path: '/users'
      fullPath: '/dashboard/users'
      preLoaderRoute: typeof DashboardUsersIndexImport
      parentRoute: typeof DashboardRouteImport
    }
  }
}

// Create and export the route tree

interface ClerkauthRouteRouteChildren {
  ClerkauthSignInRoute: typeof ClerkauthSignInRoute
  ClerkauthSignUpRoute: typeof ClerkauthSignUpRoute
}

const ClerkauthRouteRouteChildren: ClerkauthRouteRouteChildren = {
  ClerkauthSignInRoute: ClerkauthSignInRoute,
  ClerkauthSignUpRoute: ClerkauthSignUpRoute,
}

const ClerkauthRouteRouteWithChildren = ClerkauthRouteRoute._addFileChildren(
  ClerkauthRouteRouteChildren,
)

interface ClerkAuthenticatedRouteRouteChildren {
  ClerkAuthenticatedUserManagementRoute: typeof ClerkAuthenticatedUserManagementRoute
}

const ClerkAuthenticatedRouteRouteChildren: ClerkAuthenticatedRouteRouteChildren =
  {
    ClerkAuthenticatedUserManagementRoute:
      ClerkAuthenticatedUserManagementRoute,
  }

const ClerkAuthenticatedRouteRouteWithChildren =
  ClerkAuthenticatedRouteRoute._addFileChildren(
    ClerkAuthenticatedRouteRouteChildren,
  )

interface ClerkRouteRouteChildren {
  ClerkauthRouteRoute: typeof ClerkauthRouteRouteWithChildren
  ClerkAuthenticatedRouteRoute: typeof ClerkAuthenticatedRouteRouteWithChildren
}

const ClerkRouteRouteChildren: ClerkRouteRouteChildren = {
  ClerkauthRouteRoute: ClerkauthRouteRouteWithChildren,
  ClerkAuthenticatedRouteRoute: ClerkAuthenticatedRouteRouteWithChildren,
}

const ClerkRouteRouteWithChildren = ClerkRouteRoute._addFileChildren(
  ClerkRouteRouteChildren,
)

interface DashboardSettingsRouteRouteChildren {
  DashboardSettingsAccountRoute: typeof DashboardSettingsAccountRoute
  DashboardSettingsAppearanceRoute: typeof DashboardSettingsAppearanceRoute
  DashboardSettingsDisplayRoute: typeof DashboardSettingsDisplayRoute
  DashboardSettingsNotificationsRoute: typeof DashboardSettingsNotificationsRoute
  DashboardSettingsIndexRoute: typeof DashboardSettingsIndexRoute
}

const DashboardSettingsRouteRouteChildren: DashboardSettingsRouteRouteChildren =
  {
    DashboardSettingsAccountRoute: DashboardSettingsAccountRoute,
    DashboardSettingsAppearanceRoute: DashboardSettingsAppearanceRoute,
    DashboardSettingsDisplayRoute: DashboardSettingsDisplayRoute,
    DashboardSettingsNotificationsRoute: DashboardSettingsNotificationsRoute,
    DashboardSettingsIndexRoute: DashboardSettingsIndexRoute,
  }

const DashboardSettingsRouteRouteWithChildren =
  DashboardSettingsRouteRoute._addFileChildren(
    DashboardSettingsRouteRouteChildren,
  )

interface DashboardRouteRouteChildren {
  DashboardSettingsRouteRoute: typeof DashboardSettingsRouteRouteWithChildren
  DashboardOverviewRoute: typeof DashboardOverviewRoute
  DashboardProTableDemoRoute: typeof DashboardProTableDemoRoute
  DashboardProTableSimpleRoute: typeof DashboardProTableSimpleRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  DashboardAppsIndexRoute: typeof DashboardAppsIndexRoute
  DashboardChatsIndexRoute: typeof DashboardChatsIndexRoute
  DashboardHelpCenterIndexRoute: typeof DashboardHelpCenterIndexRoute
  DashboardTasksIndexRoute: typeof DashboardTasksIndexRoute
  DashboardUsersIndexRoute: typeof DashboardUsersIndexRoute
}

const DashboardRouteRouteChildren: DashboardRouteRouteChildren = {
  DashboardSettingsRouteRoute: DashboardSettingsRouteRouteWithChildren,
  DashboardOverviewRoute: DashboardOverviewRoute,
  DashboardProTableDemoRoute: DashboardProTableDemoRoute,
  DashboardProTableSimpleRoute: DashboardProTableSimpleRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  DashboardAppsIndexRoute: DashboardAppsIndexRoute,
  DashboardChatsIndexRoute: DashboardChatsIndexRoute,
  DashboardHelpCenterIndexRoute: DashboardHelpCenterIndexRoute,
  DashboardTasksIndexRoute: DashboardTasksIndexRoute,
  DashboardUsersIndexRoute: DashboardUsersIndexRoute,
}

const DashboardRouteRouteWithChildren = DashboardRouteRoute._addFileChildren(
  DashboardRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/clerk/': typeof ClerkauthRouteRouteWithChildren
  '/dashboard/settings': typeof DashboardSettingsRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/about': typeof marketingAboutRoute
  '/api-test': typeof marketingApiTestRoute
  '/contact': typeof marketingContactRoute
  '/features': typeof marketingFeaturesRoute
  '/pricing': typeof marketingPricingRoute
  '/privacy': typeof marketingPrivacyRoute
  '/terms': typeof marketingTermsRoute
  '/dashboard/overview': typeof DashboardOverviewRoute
  '/dashboard/pro-table-demo': typeof DashboardProTableDemoRoute
  '/dashboard/pro-table-simple': typeof DashboardProTableSimpleRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/blog/$slug': typeof marketingBlogSlugRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/dashboard/settings/account': typeof DashboardSettingsAccountRoute
  '/dashboard/settings/appearance': typeof DashboardSettingsAppearanceRoute
  '/dashboard/settings/display': typeof DashboardSettingsDisplayRoute
  '/dashboard/settings/notifications': typeof DashboardSettingsNotificationsRoute
  '/blog': typeof marketingBlogIndexRoute
  '/dashboard/apps': typeof DashboardAppsIndexRoute
  '/dashboard/chats': typeof DashboardChatsIndexRoute
  '/dashboard/help-center': typeof DashboardHelpCenterIndexRoute
  '/dashboard/settings/': typeof DashboardSettingsIndexRoute
  '/dashboard/tasks': typeof DashboardTasksIndexRoute
  '/dashboard/users': typeof DashboardUsersIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/clerk': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/about': typeof marketingAboutRoute
  '/api-test': typeof marketingApiTestRoute
  '/contact': typeof marketingContactRoute
  '/features': typeof marketingFeaturesRoute
  '/pricing': typeof marketingPricingRoute
  '/privacy': typeof marketingPrivacyRoute
  '/terms': typeof marketingTermsRoute
  '/dashboard/overview': typeof DashboardOverviewRoute
  '/dashboard/pro-table-demo': typeof DashboardProTableDemoRoute
  '/dashboard/pro-table-simple': typeof DashboardProTableSimpleRoute
  '/dashboard': typeof DashboardIndexRoute
  '/blog/$slug': typeof marketingBlogSlugRoute
  '/clerk/sign-in': typeof ClerkauthSignInRoute
  '/clerk/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/dashboard/settings/account': typeof DashboardSettingsAccountRoute
  '/dashboard/settings/appearance': typeof DashboardSettingsAppearanceRoute
  '/dashboard/settings/display': typeof DashboardSettingsDisplayRoute
  '/dashboard/settings/notifications': typeof DashboardSettingsNotificationsRoute
  '/blog': typeof marketingBlogIndexRoute
  '/dashboard/apps': typeof DashboardAppsIndexRoute
  '/dashboard/chats': typeof DashboardChatsIndexRoute
  '/dashboard/help-center': typeof DashboardHelpCenterIndexRoute
  '/dashboard/settings': typeof DashboardSettingsIndexRoute
  '/dashboard/tasks': typeof DashboardTasksIndexRoute
  '/dashboard/users': typeof DashboardUsersIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/clerk': typeof ClerkRouteRouteWithChildren
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/clerk/(auth)': typeof ClerkauthRouteRouteWithChildren
  '/clerk/_authenticated': typeof ClerkAuthenticatedRouteRouteWithChildren
  '/dashboard/settings': typeof DashboardSettingsRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-in-2': typeof authSignIn2Route
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/(marketing)/about': typeof marketingAboutRoute
  '/(marketing)/api-test': typeof marketingApiTestRoute
  '/(marketing)/contact': typeof marketingContactRoute
  '/(marketing)/features': typeof marketingFeaturesRoute
  '/(marketing)/pricing': typeof marketingPricingRoute
  '/(marketing)/privacy': typeof marketingPrivacyRoute
  '/(marketing)/terms': typeof marketingTermsRoute
  '/dashboard/overview': typeof DashboardOverviewRoute
  '/dashboard/pro-table-demo': typeof DashboardProTableDemoRoute
  '/dashboard/pro-table-simple': typeof DashboardProTableSimpleRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/(marketing)/blog/$slug': typeof marketingBlogSlugRoute
  '/clerk/(auth)/sign-in': typeof ClerkauthSignInRoute
  '/clerk/(auth)/sign-up': typeof ClerkauthSignUpRoute
  '/clerk/_authenticated/user-management': typeof ClerkAuthenticatedUserManagementRoute
  '/dashboard/settings/account': typeof DashboardSettingsAccountRoute
  '/dashboard/settings/appearance': typeof DashboardSettingsAppearanceRoute
  '/dashboard/settings/display': typeof DashboardSettingsDisplayRoute
  '/dashboard/settings/notifications': typeof DashboardSettingsNotificationsRoute
  '/(marketing)/blog/': typeof marketingBlogIndexRoute
  '/dashboard/apps/': typeof DashboardAppsIndexRoute
  '/dashboard/chats/': typeof DashboardChatsIndexRoute
  '/dashboard/help-center/': typeof DashboardHelpCenterIndexRoute
  '/dashboard/settings/': typeof DashboardSettingsIndexRoute
  '/dashboard/tasks/': typeof DashboardTasksIndexRoute
  '/dashboard/users/': typeof DashboardUsersIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/clerk'
    | '/dashboard'
    | '/clerk/'
    | '/dashboard/settings'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/about'
    | '/api-test'
    | '/contact'
    | '/features'
    | '/pricing'
    | '/privacy'
    | '/terms'
    | '/dashboard/overview'
    | '/dashboard/pro-table-demo'
    | '/dashboard/pro-table-simple'
    | '/dashboard/'
    | '/blog/$slug'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/dashboard/settings/account'
    | '/dashboard/settings/appearance'
    | '/dashboard/settings/display'
    | '/dashboard/settings/notifications'
    | '/blog'
    | '/dashboard/apps'
    | '/dashboard/chats'
    | '/dashboard/help-center'
    | '/dashboard/settings/'
    | '/dashboard/tasks'
    | '/dashboard/users'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/clerk'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/about'
    | '/api-test'
    | '/contact'
    | '/features'
    | '/pricing'
    | '/privacy'
    | '/terms'
    | '/dashboard/overview'
    | '/dashboard/pro-table-demo'
    | '/dashboard/pro-table-simple'
    | '/dashboard'
    | '/blog/$slug'
    | '/clerk/sign-in'
    | '/clerk/sign-up'
    | '/clerk/user-management'
    | '/dashboard/settings/account'
    | '/dashboard/settings/appearance'
    | '/dashboard/settings/display'
    | '/dashboard/settings/notifications'
    | '/blog'
    | '/dashboard/apps'
    | '/dashboard/chats'
    | '/dashboard/help-center'
    | '/dashboard/settings'
    | '/dashboard/tasks'
    | '/dashboard/users'
  id:
    | '__root__'
    | '/'
    | '/clerk'
    | '/dashboard'
    | '/clerk/(auth)'
    | '/clerk/_authenticated'
    | '/dashboard/settings'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/sign-in'
    | '/(auth)/sign-in-2'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/(marketing)/about'
    | '/(marketing)/api-test'
    | '/(marketing)/contact'
    | '/(marketing)/features'
    | '/(marketing)/pricing'
    | '/(marketing)/privacy'
    | '/(marketing)/terms'
    | '/dashboard/overview'
    | '/dashboard/pro-table-demo'
    | '/dashboard/pro-table-simple'
    | '/dashboard/'
    | '/(marketing)/blog/$slug'
    | '/clerk/(auth)/sign-in'
    | '/clerk/(auth)/sign-up'
    | '/clerk/_authenticated/user-management'
    | '/dashboard/settings/account'
    | '/dashboard/settings/appearance'
    | '/dashboard/settings/display'
    | '/dashboard/settings/notifications'
    | '/(marketing)/blog/'
    | '/dashboard/apps/'
    | '/dashboard/chats/'
    | '/dashboard/help-center/'
    | '/dashboard/settings/'
    | '/dashboard/tasks/'
    | '/dashboard/users/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ClerkRouteRoute: typeof ClerkRouteRouteWithChildren
  DashboardRouteRoute: typeof DashboardRouteRouteWithChildren
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authSignInRoute: typeof authSignInRoute
  authSignIn2Route: typeof authSignIn2Route
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
  marketingAboutRoute: typeof marketingAboutRoute
  marketingApiTestRoute: typeof marketingApiTestRoute
  marketingContactRoute: typeof marketingContactRoute
  marketingFeaturesRoute: typeof marketingFeaturesRoute
  marketingPricingRoute: typeof marketingPricingRoute
  marketingPrivacyRoute: typeof marketingPrivacyRoute
  marketingTermsRoute: typeof marketingTermsRoute
  marketingBlogSlugRoute: typeof marketingBlogSlugRoute
  marketingBlogIndexRoute: typeof marketingBlogIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ClerkRouteRoute: ClerkRouteRouteWithChildren,
  DashboardRouteRoute: DashboardRouteRouteWithChildren,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authSignInRoute: authSignInRoute,
  authSignIn2Route: authSignIn2Route,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
  marketingAboutRoute: marketingAboutRoute,
  marketingApiTestRoute: marketingApiTestRoute,
  marketingContactRoute: marketingContactRoute,
  marketingFeaturesRoute: marketingFeaturesRoute,
  marketingPricingRoute: marketingPricingRoute,
  marketingPrivacyRoute: marketingPrivacyRoute,
  marketingTermsRoute: marketingTermsRoute,
  marketingBlogSlugRoute: marketingBlogSlugRoute,
  marketingBlogIndexRoute: marketingBlogIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/clerk",
        "/dashboard",
        "/(auth)/forgot-password",
        "/(auth)/otp",
        "/(auth)/sign-in",
        "/(auth)/sign-in-2",
        "/(auth)/sign-up",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503",
        "/(marketing)/about",
        "/(marketing)/api-test",
        "/(marketing)/contact",
        "/(marketing)/features",
        "/(marketing)/pricing",
        "/(marketing)/privacy",
        "/(marketing)/terms",
        "/(marketing)/blog/$slug",
        "/(marketing)/blog/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/clerk": {
      "filePath": "clerk/route.tsx",
      "children": [
        "/clerk/(auth)",
        "/clerk/_authenticated"
      ]
    },
    "/dashboard": {
      "filePath": "dashboard/route.tsx",
      "children": [
        "/dashboard/settings",
        "/dashboard/overview",
        "/dashboard/pro-table-demo",
        "/dashboard/pro-table-simple",
        "/dashboard/",
        "/dashboard/apps/",
        "/dashboard/chats/",
        "/dashboard/help-center/",
        "/dashboard/tasks/",
        "/dashboard/users/"
      ]
    },
    "/clerk/(auth)": {
      "filePath": "clerk/(auth)/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/(auth)/sign-in",
        "/clerk/(auth)/sign-up"
      ]
    },
    "/clerk/_authenticated": {
      "filePath": "clerk/_authenticated/route.tsx",
      "parent": "/clerk",
      "children": [
        "/clerk/_authenticated/user-management"
      ]
    },
    "/dashboard/settings": {
      "filePath": "dashboard/settings/route.tsx",
      "parent": "/dashboard",
      "children": [
        "/dashboard/settings/account",
        "/dashboard/settings/appearance",
        "/dashboard/settings/display",
        "/dashboard/settings/notifications",
        "/dashboard/settings/"
      ]
    },
    "/(auth)/forgot-password": {
      "filePath": "(auth)/forgot-password.tsx"
    },
    "/(auth)/otp": {
      "filePath": "(auth)/otp.tsx"
    },
    "/(auth)/sign-in": {
      "filePath": "(auth)/sign-in.tsx"
    },
    "/(auth)/sign-in-2": {
      "filePath": "(auth)/sign-in-2.tsx"
    },
    "/(auth)/sign-up": {
      "filePath": "(auth)/sign-up.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/(marketing)/about": {
      "filePath": "(marketing)/about.tsx"
    },
    "/(marketing)/api-test": {
      "filePath": "(marketing)/api-test.tsx"
    },
    "/(marketing)/contact": {
      "filePath": "(marketing)/contact.tsx"
    },
    "/(marketing)/features": {
      "filePath": "(marketing)/features.tsx"
    },
    "/(marketing)/pricing": {
      "filePath": "(marketing)/pricing.tsx"
    },
    "/(marketing)/privacy": {
      "filePath": "(marketing)/privacy.tsx"
    },
    "/(marketing)/terms": {
      "filePath": "(marketing)/terms.tsx"
    },
    "/dashboard/overview": {
      "filePath": "dashboard/overview.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/pro-table-demo": {
      "filePath": "dashboard/pro-table-demo.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/pro-table-simple": {
      "filePath": "dashboard/pro-table-simple.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/": {
      "filePath": "dashboard/index.tsx",
      "parent": "/dashboard"
    },
    "/(marketing)/blog/$slug": {
      "filePath": "(marketing)/blog/$slug.tsx"
    },
    "/clerk/(auth)/sign-in": {
      "filePath": "clerk/(auth)/sign-in.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/(auth)/sign-up": {
      "filePath": "clerk/(auth)/sign-up.tsx",
      "parent": "/clerk/(auth)"
    },
    "/clerk/_authenticated/user-management": {
      "filePath": "clerk/_authenticated/user-management.tsx",
      "parent": "/clerk/_authenticated"
    },
    "/dashboard/settings/account": {
      "filePath": "dashboard/settings/account.tsx",
      "parent": "/dashboard/settings"
    },
    "/dashboard/settings/appearance": {
      "filePath": "dashboard/settings/appearance.tsx",
      "parent": "/dashboard/settings"
    },
    "/dashboard/settings/display": {
      "filePath": "dashboard/settings/display.tsx",
      "parent": "/dashboard/settings"
    },
    "/dashboard/settings/notifications": {
      "filePath": "dashboard/settings/notifications.tsx",
      "parent": "/dashboard/settings"
    },
    "/(marketing)/blog/": {
      "filePath": "(marketing)/blog/index.tsx"
    },
    "/dashboard/apps/": {
      "filePath": "dashboard/apps/index.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/chats/": {
      "filePath": "dashboard/chats/index.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/help-center/": {
      "filePath": "dashboard/help-center/index.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/settings/": {
      "filePath": "dashboard/settings/index.tsx",
      "parent": "/dashboard/settings"
    },
    "/dashboard/tasks/": {
      "filePath": "dashboard/tasks/index.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/users/": {
      "filePath": "dashboard/users/index.tsx",
      "parent": "/dashboard"
    }
  }
}
ROUTE_MANIFEST_END */
