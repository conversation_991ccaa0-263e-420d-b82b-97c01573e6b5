import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Main } from '@/components/layout/main'
import { Plus, Download, Settings, FileText, Users, Trash2 } from 'lucide-react'
import ProTable, { ProColumnType } from '@/components/pro-table'

// 模拟数据类型
interface DemoUser extends Record<string, unknown> {
  id: string
  name: string
  email: string
  age: number
  status: 'active' | 'inactive' | 'pending'
  role: 'admin' | 'user' | 'manager'
  salary: number
  joinDate: string
  lastLogin: string
  department: string
}

// 模拟数据
const generateMockData = (count: number): DemoUser[] => {
  const statuses: DemoUser['status'][] = ['active', 'inactive', 'pending']
  const roles: DemoUser['role'][] = ['admin', 'user', 'manager']
  const departments = ['技术部', '产品部', '运营部', '市场部', '人事部']
  
  return Array.from({ length: count }, (_, index) => ({
    id: `user-${index + 1}`,
    name: `用户${index + 1}`,
    email: `user${index + 1}@example.com`,
    age: 20 + Math.floor(Math.random() * 40),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    role: roles[Math.floor(Math.random() * roles.length)],
    salary: 5000 + Math.floor(Math.random() * 20000),
    joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28)).toISOString().split('T')[0],
    lastLogin: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
    department: departments[Math.floor(Math.random() * departments.length)],
  }))
}

export default function ProTableDemo() {
  const [mockData] = useState(() => generateMockData(100))
  const [loading, setLoading] = useState(false)

  // 基础表格列配置
  const basicColumns: ProColumnType<DemoUser>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 100,
      search: false,
      sorter: false,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      search: {
        placeholder: '请输入姓名',
      },
      sorter: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      search: {
        placeholder: '请输入邮箱',
      },
      sorter: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      valueType: 'number',
      width: 80,
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      width: 100,
      filters: true,
      valueEnum: {
        active: '激活',
        inactive: '未激活',
        pending: '待审核',
      },
      render: (value) => {
        const colorMap = {
          active: 'bg-green-100 text-green-800',
          inactive: 'bg-gray-100 text-gray-800',
          pending: 'bg-yellow-100 text-yellow-800',
        }
        return (
          <Badge className={colorMap[value as keyof typeof colorMap]}>
            {value === 'active' ? '激活' : value === 'inactive' ? '未激活' : '待审核'}
          </Badge>
        )
      },
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'actions',
      width: 120,
      render: (_, record) => (
        <div className="flex space-x-2">
          <Button size="sm" variant="outline" onClick={() => console.log('编辑', record)}>
            编辑
          </Button>
          <Button size="sm" variant="outline" onClick={() => console.log('删除', record)}>
            删除
          </Button>
        </div>
      ),
    },
  ]

  // 高级表格列配置
  const advancedColumns: ProColumnType<DemoUser>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 100,
      search: false,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 120,
      search: {
        placeholder: '请输入姓名',
      },
      sorter: true,
      fixed: 'left',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: 200,
      search: {
        placeholder: '请输入邮箱',
      },
      ellipsis: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      valueType: 'number',
      width: 80,
      sorter: true,
    },
    {
      title: '薪资',
      dataIndex: 'salary',
      valueType: 'money',
      width: 120,
      sorter: true,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      width: 100,
      filters: true,
      valueEnum: {
        active: '激活',
        inactive: '未激活',
        pending: '待审核',
      },
    },
    {
      title: '角色',
      dataIndex: 'role',
      valueType: 'select',
      width: 100,
      filters: true,
      valueEnum: {
        admin: '管理员',
        user: '普通用户',
        manager: '经理',
      },
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 120,
      search: {
        placeholder: '请选择部门',
      },
    },
    {
      title: '入职日期',
      dataIndex: 'joinDate',
      valueType: 'date',
      width: 120,
      sorter: true,
      search: false,
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      valueType: 'dateTime',
      width: 180,
      sorter: true,
      search: false,
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <div className="flex space-x-1">
          <Button size="sm" variant="outline" onClick={() => console.log('查看', record)}>
            查看
          </Button>
          <Button size="sm" variant="outline" onClick={() => console.log('编辑', record)}>
            编辑
          </Button>
          <Button size="sm" variant="outline" onClick={() => console.log('删除', record)}>
            删除
          </Button>
        </div>
      ),
    },
  ]

  // 模拟远程数据请求
  const mockRequest = async (params: Record<string, unknown>) => {
    console.log('请求参数:', params)
    setLoading(true)
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const { current = 1, pageSize = 10, search, ...filters } = params
    let filteredData = [...mockData]

    // 类型安全的参数处理
    const currentPage = typeof current === 'number' ? current : 1
    const pageSizeNum = typeof pageSize === 'number' ? pageSize : 10

    // 模拟搜索
    if (search && typeof search === 'string') {
      filteredData = filteredData.filter(item =>
        item.name.includes(search) || item.email.includes(search)
      )
    }

    // 模拟筛选
    Object.entries(filters).forEach(([key, value]) => {
      if (value && Array.isArray(value)) {
        filteredData = filteredData.filter(item => value.includes(item[key as keyof DemoUser]))
      }
    })

    // 模拟分页
    const start = (currentPage - 1) * pageSizeNum
    const end = start + pageSizeNum
    const pageData = filteredData.slice(start, end)
    
    setLoading(false)
    
    return {
      data: pageData,
      total: filteredData.length,
      success: true,
    }
  }

  return (
    <Main>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">ProTable 组件演示</h1>
          <p className="text-muted-foreground mt-2">
            基于 @tanstack/react-table 和 shadcn/ui 的高级表格组件
          </p>
        </div>

        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList>
            <TabsTrigger value="basic">基础表格</TabsTrigger>
            <TabsTrigger value="advanced">高级表格</TabsTrigger>
            <TabsTrigger value="remote">远程数据</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>基础表格示例</CardTitle>
                <CardDescription>
                  展示基本的表格功能：排序、筛选、分页、行选择
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProTable<DemoUser>
                  columns={basicColumns}
                  dataSource={mockData.slice(0, 20)}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                  }}
                  rowSelection={{
                    type: 'checkbox',
                    onChange: (keys, rows) => {
                      console.log('选中的行:', keys, rows)
                    },
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>高级表格示例</CardTitle>
                <CardDescription>
                  展示高级功能：高级搜索、固定列、多种数据类型、工具栏
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProTable<DemoUser>
                  columns={advancedColumns}
                  dataSource={mockData}
                  rowKey="id"
                  search={{
                    labelWidth: 80,
                    collapsed: false,
                  }}
                  pagination={{
                    pageSize: 15,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `显示 ${range[0]}-${range[1]} 项，共 ${total} 项`,
                  }}
                  rowSelection={{
                    type: 'checkbox',
                  }}
                  toolbar={{
                    refresh: true,
                    setting: true,
                    actions: [
                      <Button key="add" size="sm">
                        <Plus className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">新增用户</span>
                      </Button>,
                      <Button key="export" variant="outline" size="sm">
                        <Download className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">导出数据</span>
                      </Button>,
                      <Button key="import" variant="outline" size="sm">
                        <FileText className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">导入数据</span>
                      </Button>,
                      <Button key="batch" variant="outline" size="sm">
                        <Users className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">批量操作</span>
                      </Button>,
                      <Button key="delete" variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">批量删除</span>
                      </Button>,
                    ],
                  }}
                  scroll={{ x: 1400 }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="remote" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>远程数据示例</CardTitle>
                <CardDescription>
                  展示远程数据加载、服务端分页、搜索、筛选
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProTable<DemoUser>
                  columns={basicColumns}
                  request={mockRequest}
                  loading={loading}
                  rowKey="id"
                  search={true}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                  }}
                  toolbar={{
                    refresh: true,
                    setting: true,
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Main>
  )
}
