import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Main } from '@/components/layout/main'
import ProTable, { ProColumnType } from '@/components/pro-table'

// 简单的数据类型
interface SimpleUser extends Record<string, unknown> {
  id: string
  name: string
  email: string
  status: 'active' | 'inactive'
  age: number
}

// 简单的模拟数据
const simpleData: SimpleUser[] = [
  { id: '1', name: '张三', email: 'zhang<PERSON>@example.com', status: 'active', age: 25 },
  { id: '2', name: '李四', email: '<EMAIL>', status: 'inactive', age: 30 },
  { id: '3', name: '王五', email: '<EMAIL>', status: 'active', age: 28 },
  { id: '4', name: '赵六', email: '<EMAIL>', status: 'active', age: 35 },
  { id: '5', name: '钱七', email: 'q<PERSON><PERSON>@example.com', status: 'inactive', age: 22 },
]

export default function SimpleProTableDemo() {
  const [loading] = useState(false)

  // 简单的列配置
  const columns: ProColumnType<SimpleUser>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      search: {
        placeholder: '请输入姓名',
      },
      sorter: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      search: {
        placeholder: '请输入邮箱',
      },
    },
    {
      title: '年龄',
      dataIndex: 'age',
      valueType: 'number',
      width: 80,
      sorter: true,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      width: 100,
      filters: true,
      valueEnum: {
        active: '激活',
        inactive: '未激活',
      },
      render: (value) => {
        const colorMap = {
          active: 'bg-green-100 text-green-800',
          inactive: 'bg-gray-100 text-gray-800',
        }
        return (
          <Badge className={colorMap[value as keyof typeof colorMap]}>
            {value === 'active' ? '激活' : '未激活'}
          </Badge>
        )
      },
    },
    {
      title: '操作',
      key: 'actions',
      valueType: 'actions',
      width: 120,
      render: (_, record) => (
        <div className="flex space-x-2">
          <Button size="sm" variant="outline">
            编辑
          </Button>
          <Button size="sm" variant="outline">
            删除
          </Button>
        </div>
      ),
    },
  ]

  return (
    <Main>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">ProTable 简单演示</h1>
          <p className="text-muted-foreground mt-2">
            基础功能测试：表格显示、排序、筛选、分页
          </p>
        </div>

        <div className="space-y-4">
          <ProTable<SimpleUser>
            columns={columns}
            dataSource={simpleData}
            loading={loading}
            rowKey="id"
            search={true}
            pagination={{
              pageSize: 3,
              showSizeChanger: true,
            }}
            rowSelection={{
              type: 'checkbox',
            }}
            toolbar={{
              refresh: true,
              setting: true,
            }}
          />
        </div>
      </div>
    </Main>
  )
}
